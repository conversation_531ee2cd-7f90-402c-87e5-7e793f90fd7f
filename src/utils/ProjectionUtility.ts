const { MetricUtility } = require('./MetricUtility');
const { fetchDraftKings } = require('../sharps/DraftKings');
const { fetchBetRivers } = require('../sharps/BetRivers');
const { fetchCaesars } = require('../sharps/Caesars');
const { fetchFanduel } = require('../sharps/FanduelSharp');
const { fetchCirca } = require('../sharps/Circa');
const { fetchBetOnline } = require('../sharps/BetOnline');
const { fetchPinnyProps } = require('../sharps/Pinnacle');
const fs = require('fs');
const path = require('path');

// Create debug log file
const debugLogPath = path.join(__dirname, '../../logs/debug.log');
fs.mkdirSync(path.dirname(debugLogPath), { recursive: true });

function writeDebugLog(message: string) {
    const timestamp = new Date().toISOString();
    fs.appendFileSync(debugLogPath, `${timestamp} ${message}\n`);
}

import dayjs from "./date";
import { MergedProjection, Projection, SharpEV, Translation, Sharp } from './types';
import { translateStat } from "./MappingUtility";
import { calculateSharpEVs } from "../sharps/helper";
import logger from "./logger";
import { formatDecimal } from "./FormatUtility";

const DEFAULT_START_TIME_VARIANCE_MS = 3_600_000; // 1 hour
const GOLF_START_TIME_VARIANCE_MS = 7_200_000; // 2 hours
const SOCCER_TIME_VARIANCE = 24 * 60 * 60 * 1000; // 24 hours
const BASEBALL_TIME_VARIANCE_MS = 1_200_000; // 20 minutes

// Manual mappings for players that share the same name across teams.
// The keys are normalized player names, and each maps teams/keywords to a
// unique identifier.
const duplicateNameMap: Record<string, Record<string, string>> = {
    maxmuncy: {
        dodgers: 'max_muncy_dodgers',
        athletics: 'max_muncy_athletics'
    }
};

function normalizePlayerName(name: string, provider: string, team?: string, matchup?: string): string {
    if (typeof name !== 'string') {
        console.warn(`Invalid player name from provider "${provider}":`, name);
        return '';
    }

    let normalized = name
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '');

    const possibleTeamString = `${team ?? ''} ${matchup ?? ''}`.toLowerCase();
    const mapping = duplicateNameMap[normalized];
    if (mapping) {
        for (const key of Object.keys(mapping)) {
            if (possibleTeamString.includes(key)) {
                return mapping[key];
            }
        }
    }

    return normalized;
}

function normalizeStatType(statType: string): string {
    if (!statType) return '';
    // Remove extra spaces and trim
    return statType.replace(/\s+/g, ' ').trim();
}

function calculateQuarterKelly(probability: number, decimalOdds: number): number {
    const b = decimalOdds - 1;
    const q = 1 - probability;
    const kellyFraction = (b * probability - q) / b;
    return kellyFraction * 0.25;
}

// Helper function to safely parse percentage strings (e.g., "55.2%") to decimals (e.g., 0.552)
function parsePercent(percentString: string | undefined): number | null {
    if (!percentString) return null;
    const num = parseFloat(percentString.replace('%', ''));
    return !isNaN(num) ? num / 100 : null;
}

function safeFetch(fn: () => Promise<Projection[]>, timeoutMs = 75000) {
    return Promise.race([
      fn(),
      new Promise<Projection[]>((_, reject) =>
        setTimeout(() => reject(new Error('Sharp fetch timeout')), timeoutMs)
      )
    ]);
  }

/* Add a retry mechanism for fetching sharp data */
async function safeFetchWithRetry(fn: () => Promise<Projection[]>, timeoutMs = 75000, retries = 1): Promise<Projection[]> {
  let attempt = 0;
  while (attempt <= retries) {
    try {
      if (attempt === 0) {
         logger(`[SharpFetch] Starting fetch attempt for ${fn.name || "unknown function"}`);
      } else {
         logger(`[SharpFetch] Retry attempt ${attempt} for ${fn.name || "unknown function"}`);
      }
      return await safeFetch(fn, timeoutMs);
    } catch (error: any) {
      logger(`[SharpFetch] Attempt ${attempt + 1} failed for ${fn.name || "unknown function"}: ${error.message || error}`);
      attempt++;
      if (attempt > retries) {
        logger(`[SharpFetch] All attempts exhausted for ${fn.name || "unknown function"}.`);
        throw error;
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  throw new Error('Unexpected error in safeFetchWithRetry');
}

// Update sharp projections fetching to use safeFetchWithRetry
export async function fetchSharpProjections(): Promise<Projection[]> {
    const results = await Promise.allSettled([
        safeFetchWithRetry(fetchFanduel),
        safeFetchWithRetry(fetchBetOnline),
        safeFetchWithRetry(fetchDraftKings),
        safeFetchWithRetry(fetchCaesars),
        safeFetchWithRetry(fetchBetRivers),
        safeFetchWithRetry(fetchCirca),
        safeFetchWithRetry(fetchPinnyProps),
    ]);

    const sharpProjections: Projection[] = results
        .filter(result => result.status === 'fulfilled')
        .map(result => (result as PromiseFulfilledResult<Projection[]>).value)
        .flat();

    return sharpProjections;
}

export function processProjections(sharpProjections: Projection[], providerProjections: Projection[], providerName: string) {
    const processedProjections: MergedProjection[] = [];
    const translateCache = new Map<string, Translation>();

    const sharpProjectionMap = new Map<string, Projection[]>();

    // Build sharpProjectionMap (No changes needed here)
    sharpProjections.forEach(sharpProjection => {
        const normalizedPlayerName = normalizePlayerName(
            sharpProjection.player_name,
            sharpProjection.source,
            (sharpProjection as any).team,
            sharpProjection.matchup
        );
        const translationKey = `${sharpProjection.league}-${sharpProjection.stat_type}-${sharpProjection.source}`;
        let translation = translateCache.get(translationKey);

        if (!translation) {
            const newTranslation = translateStat(
                sharpProjection.league,
                sharpProjection.source,
                sharpProjection.stat_type,
                (sharpProjection as any).stat_type_normalized
            );
            translation = newTranslation;
            translateCache.set(translationKey, newTranslation);
        }

        const standardStatType = translation?.standardStatType;
        const key = `${sharpProjection.league}-${normalizedPlayerName}-${standardStatType}`;

        if (!sharpProjectionMap.has(key)) {
            sharpProjectionMap.set(key, [sharpProjection]);
        } else {
            sharpProjectionMap.get(key)?.push(sharpProjection);
        }
    });

    const processProjection = (providerProjection: Projection) => {
        if (!providerProjection.player_name) {
            return;
        }

        const isGolfProjection = providerProjection.league === 'GOLF';
        const normalizedProviderName = normalizePlayerName(
            providerProjection.player_name,
            providerProjection.source,
            providerProjection.team,
            providerProjection.matchup
        );
        const translationKey = `${providerProjection.league}-${providerProjection.stat_type}-${providerProjection.source}`;
        let translation = translateCache.get(translationKey);

        if (!translation) {
            translation = translateStat(
                providerProjection.league,
                providerProjection.source,
                providerProjection.stat_type,
                (providerProjection as any).stat_type_normalized
            );
            translateCache.set(translationKey, translation!);
        }

        const { standardStatType, modelInfo, preferredSharp } = translation!;

        const sharpKey = `${providerProjection.league}-${normalizedProviderName}-${standardStatType}`;
        let matchedSharpProjections = sharpProjectionMap.get(sharpKey) ?? [];

        // Golf fuzzy matching (keep as is)
        if (isGolfProjection && matchedSharpProjections.length === 0) {
             // ... (existing golf fuzzy matching logic) ...
             for (const [key, projections] of sharpProjectionMap.entries()) {
                if (key.toUpperCase().startsWith('GOLF-')) {
                    const keyParts = key.split('-');
                    const sharpPlayerName = keyParts[1];
                    const sharpStatType = keyParts[2];
                    if (normalizedProviderName.substring(0, 5) === sharpPlayerName.substring(0, 5)) {
                        if (normalizeStatType(sharpStatType).toLowerCase() === normalizeStatType(standardStatType).toLowerCase()) {
                            matchedSharpProjections = projections;
                            break;
                        }
                    }
                }
            }
        }

        if (matchedSharpProjections.length === 0) {
            return;
        }

        // --- NOVIG REQUIREMENT: Must have exact sharp line match AND sufficient limit ---
        if (providerProjection.source === 'Novig') {
            const hasExactLineMatch = matchedSharpProjections.some(sharp =>
                // Use a small tolerance for floating point comparisons
                Math.abs((sharp.line ?? -1) - (providerProjection.line ?? -2)) < 0.01
            );
            if (!hasExactLineMatch) {
                // Log for debugging
                writeDebugLog(`  --> Novig projection excluded: Line ${providerProjection.line} has no exact sharp line match. Player: ${providerProjection.player_name}, Stat: ${standardStatType}`);
                return; // Skip this Novig projection
            }

            // Check for minimum limit
            const minLimit = 5; // $5 minimum limit
            const overLimit = providerProjection.novig_over_limit ?? 0;
            const underLimit = providerProjection.novig_under_limit ?? 0;
            if (!(overLimit >= minLimit || underLimit >= minLimit)) {
                writeDebugLog(`  --> Novig projection excluded: Insufficient limit (Over: ${overLimit}, Under: ${underLimit} < ${minLimit}). Player: ${providerProjection.player_name}, Stat: ${standardStatType}, Line: ${providerProjection.line}`);
                return; // Skip this Novig projection
            }
        }
        // --- END NOVIG REQUIREMENT ---

        // --- PROPHETX REQUIREMENT: Must have exact sharp line match AND sufficient limit ---
        if (providerProjection.source === 'ProphetX') {
            const hasExactLineMatch = matchedSharpProjections.some(sharp =>
                // Use a small tolerance for floating point comparisons
                Math.abs((sharp.line ?? -1) - (providerProjection.line ?? -2)) < 0.01
            );
            if (!hasExactLineMatch) {
                // Log for debugging
                writeDebugLog(`  --> ProphetX projection excluded: Line ${providerProjection.line} has no exact sharp line match. Player: ${providerProjection.player_name}, Stat: ${standardStatType}`);
                return; // Skip this ProphetX projection
            }

            // Limit check is moved to final filtering stage
        }
        // --- END PROPHETX REQUIREMENT ---

        if (!modelInfo || !modelInfo.model || !['negbin', 'poisson'].includes(modelInfo.model)) {
            return;
        }

        // --- Step 1: Create `sharpsForStorage` with only essential filters ---
        const sharpsForStorage = matchedSharpProjections.filter(sharpProjection => {
            let keep = true;
            let reason = "";

            // Check if game has already started
            const currentTime = Date.now();
            const providerStartTime = dayjs(providerProjection.start_time).valueOf();
            if (providerStartTime < currentTime) {
                keep = false;
                reason = `Game already started (${dayjs(providerProjection.start_time).format('YYYY-MM-DD HH:mm:ss')} < ${dayjs(currentTime).format('YYYY-MM-DD HH:mm:ss')})`;
            }

            // Time Check
            const startTimeVariance = 
                providerProjection.league === 'GOLF' ? GOLF_START_TIME_VARIANCE_MS
                : providerProjection.league === 'SOCCER' ? SOCCER_TIME_VARIANCE
                : providerProjection.league === 'MLB' ? BASEBALL_TIME_VARIANCE_MS
                : DEFAULT_START_TIME_VARIANCE_MS;
            
            const providerTime = dayjs(providerProjection.start_time).valueOf();
            const sharpTime = dayjs(sharpProjection.start_time).valueOf();
            const timeDiff = Math.abs(sharpTime - providerTime);
            
            if (timeDiff > startTimeVariance) {
                keep = false;
                reason = `Time Variance (${timeDiff}ms > ${startTimeVariance}ms)`;
            }

            // Source Check
            if (keep && ((providerProjection.source === sharpProjection.source) || (providerProjection.source === 'PropBuilder' && sharpProjection.source === 'BetOnline'))) {
                keep = false;
                reason = `Source Match (${providerProjection.source} vs ${sharpProjection.source})`;
            }

            // Invalid Odds Check
            if (keep && (
                sharpProjection.over_odds_american === null || sharpProjection.over_odds_american === 'N/A' || sharpProjection.over_odds_american === '**/' ||
                sharpProjection.under_odds_american === null || sharpProjection.under_odds_american === 'N/A' || sharpProjection.under_odds_american === '**/' ||
                sharpProjection.over_odds_decimal === undefined || sharpProjection.under_odds_decimal === undefined
            )) {
                keep = false;
                reason = `Invalid Odds (${sharpProjection.over_odds_american}/${sharpProjection.under_odds_american})`;
            }

             // Debug log if DK is filtered out at this basic stage
             if (!keep && providerProjection.league === 'MLB' && sharpProjection.source === 'DraftKings') {
                 writeDebugLog(`    [Basic Filter Fail DK]: ${reason}`);
             }

            return keep;
        });

        if (sharpsForStorage.length === 0) {
             if (providerProjection.league === 'MLB') {
                const reason = matchedSharpProjections.length > 0 ? "basic filtering (time/source/odds)" : "no initial match";
                writeDebugLog(`  --> No sharps left after ${reason}. Skipping projection for ${providerProjection.player_name} ${standardStatType}.`);
             }
             return; // Skip if no valid sharps remain after basic filtering
        }

        // --- NEW/UPDATED: Strict Line Matching for MLB Home Runs & NHL Goals (Applied AFTER basic filters) ---
        const isMlbHr = providerProjection.league === 'MLB' && standardStatType === 'Home Runs';
        const isNhlGoal = providerProjection.league === 'NHL' && standardStatType === 'Goals'; // Define standardStatType for Goals

        if (isMlbHr || isNhlGoal) {
            const hasExactLineMatchInStorage = sharpsForStorage.some(sharp =>
                // Use a small tolerance for floating point comparisons
                Math.abs((sharp.line ?? -1) - (providerProjection.line ?? -2)) < 0.01
            );
            if (!hasExactLineMatchInStorage) {
                 // Updated log message to be more generic
                 writeDebugLog(`  --> ${providerProjection.league} ${standardStatType} projection excluded: No sharp in storage list (${sharpsForStorage.length} checked) has exact line ${providerProjection.line}. Provider: ${providerProjection.source}, Player: ${providerProjection.player_name}`);
                 const linesInStorage = sharpsForStorage.map(s => s.line).join(', ');
                 writeDebugLog(`      Lines found in storage: [${linesInStorage}]`);
                 return; // Skip this projection entirely
            }
             // Updated log message to be more generic
             writeDebugLog(`  --> ${providerProjection.league} ${standardStatType} projection PASSED strict line match for line ${providerProjection.line}. Proceeding...`);
        }
        // --- END UPDATED RULE ---

        // Verify model info *after* the specific HR/Goal check, as HRs/Goals need this check regardless of model
        if (!modelInfo || !modelInfo.model || !['negbin', 'poisson'].includes(modelInfo.model)) {
            if (providerProjection.league === 'MLB') writeDebugLog(`  --> Invalid or missing modelInfo (${modelInfo?.model}). Skipping EV calc.`);
            return; // Skip if model info is invalid
        }

        // --- Step 2: Sort Sharps for EV Calculation Preference ---
        // Sort the storage list itself, this doesn't remove items
        sharpsForStorage.sort((a, b) => {
             // Priority 1: Exact line match
             const aExactMatch = a.line === providerProjection.line;
             const bExactMatch = b.line === providerProjection.line;
             if (aExactMatch !== bExactMatch) return bExactMatch ? 1 : -1;

             // Priority 2: Preferred sharp
             const aPreferred = a.source === preferredSharp;
             const bPreferred = b.source === preferredSharp;
             if (aPreferred !== bPreferred) return bPreferred ? 1 : -1;

             // Priority 3: Main line vs alt line
             const aMainLine = a.is_alt === false || !a.hasOwnProperty('is_alt');
             const bMainLine = b.is_alt === false || !b.hasOwnProperty('is_alt');
             if (aMainLine !== bMainLine) return bMainLine ? 1 : -1;

             return 0; // Keep original order if all priorities are equal
        });


        // --- Step 3: Determine Sharps for EV Calculation (Prioritize Exact Line) ---
        const exactLineSharps = sharpsForStorage.filter( // Filter from the now-sorted storage list
            (s) => Math.abs((s.line ?? -1) - (providerProjection.line ?? -2)) < 0.01 // Use tolerance for float comparison
        );
        // Use exact matches if available, otherwise use ALL valid sharps from storage
        const sharpsForEvCalc = exactLineSharps.length > 0 ? exactLineSharps : sharpsForStorage;

        // --- Step 4: Calculate EV using the chosen subset ---
        let probabilityFunction;
        if (modelInfo.model === 'negbin') {
            probabilityFunction = (k, r) => MetricUtility.negativeBinomialPmf(k, r, modelInfo.p);
        } else if (modelInfo.model === 'poisson') {
            probabilityFunction = MetricUtility.poissonPmf;
        } else {
            // Handle case where model is not defined or invalid - maybe skip?
             if (providerProjection.league === 'MLB') writeDebugLog(`  --> Invalid or missing modelInfo. Skipping EV calc.`);
             return;
        }

        const sharpEvs = calculateSharpEVs(
            sharpsForEvCalc,
            providerProjection,
            probabilityFunction
        );

        // --- Step 5: Augment the `sharpsForStorage` list ---
        sharpsForStorage.forEach(sharp => {
            const sharpEv = sharpEvs.find(ev => ev.source === sharp.source);
            const sharpWithFields = sharp as Sharp;

            if (sharpEv) {
                // 3% commission on ProphetX wins
                if (sharpEv.source === 'ProphetX') {
                    // Figure out which side (Over or Under) we're betting
                    const betSide = sharpEv.datawise_pick.toLowerCase();
                    // Grab the correct decimal odds from the providerProjection, default to 1 if undefined
                    const oddsForBetSide = (betSide === 'over'
                        ? providerProjection.over_odds_decimal
                        : providerProjection.under_odds_decimal) ?? 1;

                    // datawise_percent is like "65%", so parse out the numeric probability
                    const probOfWinning = parseFloat(sharpEv.datawise_percent) / 100;

                    // Subtract 3% * p * (decimalOdds – 1) from the EV
                    sharpEv.ev -= 0.03 * probOfWinning * (oddsForBetSide - 1);

                    // Recompute its display percentage
                    sharpEv.ev_display = (sharpEv.ev * 100).toFixed(2) + '%';
                }

                sharpWithFields.datawise_pick = sharpEv.datawise_pick;
                sharpWithFields.ev = sharpEv.ev;
                sharpWithFields.ev_display = sharpEv.ev_display;
                const betSide = sharpEv.datawise_pick.toLowerCase();
                const oddsForBetSide = (betSide === 'over'
                    ? providerProjection.over_odds_decimal
                    : providerProjection.under_odds_decimal) ?? 1;
                sharpWithFields.qk = 0.25 * (sharpEv.ev / (oddsForBetSide - 1));
                sharpWithFields.qk_display = (sharpWithFields.qk * 100).toFixed(1) + 'U';
            } else {
                // Clear potentially stale data if not used in EV calc
                sharpWithFields.datawise_pick = undefined;
                sharpWithFields.ev = undefined;
                sharpWithFields.ev_display = undefined;
                sharpWithFields.qk = undefined;
                sharpWithFields.qk_display = undefined;
            }
        });

        // --- Step 6: Calculate Overall Metrics based on `sharpEvs` ---
        const sharpOrder = [preferredSharp, 'Fanduel', 'DraftKings', 'Pinnacle', 'Caesars', 'BetOnline', 'BetRivers', 'Circa', 'ESPNBet'].filter((value, index, self) => self.indexOf(value) === index);

        let preferredSharpEv: SharpEV | undefined;
        for (const sharp of sharpOrder) {
            preferredSharpEv = sharpEvs.find((ev) => ev.source === sharp);
            if (preferredSharpEv) break;
        }

        if (!preferredSharpEv) {
            if (providerProjection.league === 'MLB') writeDebugLog(`  --> Could not determine preferred sharp EV from ${sharpEvs.length} EV results. Skipping.`);
            return; // Need a preferred EV to proceed
        }

        const fanduelEv = sharpEvs.find(sharp => sharp.source === "Fanduel");
        const dkEv = sharpEvs.find(sharp => sharp.source === "DraftKings");
        let nbaEv: number | undefined;
        if (preferredSharp === "Fanduel" && dkEv && fanduelEv) {
            nbaEv = (fanduelEv.ev + dkEv.ev) / 2;
        }

        const avg_ev = sharpEvs.reduce((acc, cur) => acc + cur.ev, 0) / sharpEvs.length;
        const avg_ev_display = formatDecimal(100 * avg_ev) + '%';
        const sharpEv = nbaEv ?? preferredSharpEv.ev; // Use preferredSharpEv.ev as fallback
        const sharpEvDisplay = formatDecimal(100 * sharpEv!) + '%';

        let fairOdds: { over: string, under?: string };
        if (providerProjection.under_odds_american) {
            fairOdds = calculateFairOdds(sharpEvs, providerProjection.line); // Use sharpEvs for fair odds, pass line
        } else {
            // For one-way props, use sharpsForEvCalc as it might contain non-exact lines if necessary
            fairOdds = calculateOneWayPropFairOdds(sharpsForEvCalc);
        }

        const overUnderIndicator = preferredSharpEv.datawise_pick.toLowerCase() === 'over' ? 'o' : 'u';
        const oddsForBetSide = (overUnderIndicator === 'o'
            ? providerProjection.over_odds_decimal
            : providerProjection.under_odds_decimal) ?? 1;
        const qk = 0.25 * (avg_ev / (oddsForBetSide - 1)); // Use avg_ev for QK
        const qkDisplay = (qk * 100).toFixed(1) + 'U';
        let projString = `${providerProjection.proj_id}-${overUnderIndicator}-${providerProjection.line}`;

        // --- Step 7: Create Merged Projection using `sharpsForStorage` ---
        const mergedProjection = {
            ...providerProjection,
            stat_type: standardStatType,
            modelInfo,
            preferred_sharp: preferredSharp,
            fairOdds,
            sharps: sharpsForStorage, // <-- Save the inclusive list
            sharp_ev: sharpEv!,
            sharp_ev_display: sharpEvDisplay,
            avg_ev,
            avg_ev_display,
            qk,
            qk_display: qkDisplay,
            datawise_pick: preferredSharpEv.datawise_pick,
            datawise_percent: preferredSharpEv.datawise_percent,
            model_over_percent: preferredSharpEv.model_over_percent,
            model_under_percent: preferredSharpEv.model_under_percent,
            model_tie_percent: preferredSharpEv.model_tie_percent,
            model_ev_under: preferredSharpEv.model_ev_under,
            model_ev_over: preferredSharpEv.model_ev_over,
            proj_string: projString,
        };

        // --- Final Filtering and Adding to results ---
        const { source, stat_type: currentStatType } = mergedProjection;
        if (
            source === 'HardRock' &&
            (currentStatType.toUpperCase() === 'HOME RUNS' || currentStatType.toUpperCase() === 'STOLEN BASES') &&
            mergedProjection.datawise_pick === 'Under'
        ) {
            if (providerProjection.league === 'MLB') writeDebugLog(`  --> Skipping HardRock Under HR/SB.`);
            return;
        }

        if ((mergedProjection.sharp_ev >= 0 || mergedProjection.avg_ev >= 0) && mergedProjection.qk > -0.5) {
            let allowProjection = true; // Flag to control if projection is added

            // --- NEW: Universal BetRivers MLB Rule ---
            if (allowProjection && providerProjection.source === 'BetRivers' && providerProjection.league === 'MLB') {
                 const hasSharpWithSameLine = sharpsForStorage.some(sharp =>
                     Math.abs(sharp.line - providerProjection.line) < 0.01
                 );
                 if (!hasSharpWithSameLine) {
                    writeDebugLog(`  --> MLB BetRivers projection excluded: No sharp in storage with matching line ${providerProjection.line} for ${currentStatType}.`);
                    allowProjection = false; // Do not add this projection
                 }
            }


            // --- Existing HITS Rule (Apply only if NOT BetRivers, as BetRivers is handled above) ---
            if (allowProjection && providerProjection.source !== 'BetRivers' && providerProjection.league === 'MLB' && currentStatType.toUpperCase() === 'HITS') {
                 // Use sharpsForStorage to check against all potentially valid sharps
                const hasSharpWithSameLine = sharpsForStorage.some(sharp =>
                    Math.abs(sharp.line - providerProjection.line) < 0.01
                );
                if (!hasSharpWithSameLine) {
                    writeDebugLog(`  --> MLB HITS projection excluded (Non-BetRivers): No sharp in storage with matching line value ${providerProjection.line}`);
                    allowProjection = false;
                }
            }

            // --- Existing Total Bases Rule (Apply only if NOT BetRivers) ---
            if (allowProjection && providerProjection.source !== 'BetRivers' && providerProjection.league === 'MLB' && currentStatType.toUpperCase() === 'TOTAL BASES') { // Use the standardized type
                 // Use sharpsForStorage to check against all potentially valid sharps
                 const hasSharpWithSameLine = sharpsForStorage.some(sharp =>
                     Math.abs(sharp.line - providerProjection.line) < 0.01
                 );
                 if (!hasSharpWithSameLine) {
                     writeDebugLog(`  --> MLB Total Bases projection excluded (Non-BetRivers): No sharp in storage with matching line value ${providerProjection.line}. Provider: ${providerProjection.source}, Sharps Checked: ${sharpsForStorage.length}`);
                     allowProjection = false;
                 }
            }

            // --- NEW: Pitcher Outs strict line match rule ---
            if (allowProjection && currentStatType.toUpperCase() === 'PITCHER OUTS') {
                const hasExactSharpLine = sharpsForStorage.some(sharp =>
                    Math.abs((sharp.line ?? -1) - (providerProjection.line ?? -2)) < 0.01
                );
                if (!hasExactSharpLine) {
                    writeDebugLog(`  --> Pitcher Outs projection excluded: No sharp with exact line ${providerProjection.line}. Provider: ${providerProjection.source}, Player: ${providerProjection.player_name}`);
                    allowProjection = false;
                }
            }

            // ***** START: NEW ProphetX Limit Check for Recommended Side *****
            if (allowProjection && mergedProjection.source === 'ProphetX') {
                const minLimit = 5; // $5 minimum limit
                const pickSide = mergedProjection.datawise_pick.toLowerCase();
                let relevantLimit = 0;

                if (pickSide === 'over') {
                    relevantLimit = providerProjection.prophetx_over_limit ?? 0;
                } else if (pickSide === 'under') {
                    relevantLimit = providerProjection.prophetx_under_limit ?? 0;
                }

                if (relevantLimit < minLimit) {
                    writeDebugLog(`  --> ProphetX projection excluded: Limit for recommended side '${pickSide}' (${relevantLimit}) is less than ${minLimit}. Player: ${mergedProjection.player_name}, Stat: ${mergedProjection.stat_type}, Line: ${mergedProjection.line}`);
                    allowProjection = false; // Do not add this projection
                }
            }
            // ***** END: NEW ProphetX Limit Check for Recommended Side *****

            // Add the projection if all checks passed
            if (allowProjection) {
                 processedProjections.push(mergedProjection);
            } else {
                 // Log already happens inside the specific filter rule causing exclusion
            }
        } else { // Log why it failed the general EV/QK check
             if (providerProjection.league === 'MLB') {
                 writeDebugLog(`  --> Projection excluded by EV/QK filter. Sharp EV: ${sharpEvDisplay}, Avg EV: ${avg_ev_display}, QK: ${qkDisplay}`);
             }
        }
    };

    providerProjections.forEach(processProjection);

    return processedProjections;
}

function calculateFairOdds(sharpSources: SharpEV[], line: number): { over: string, under?: string } {
    const sources = sharpSources.length;
    if (sources === 0) {
        return { over: '+100', under: '+100' };
    }

    let totalOverPercent = 0, totalUnderPercent = 0, totalTiePercent = 0;
    let validSourceCount = 0;

    sharpSources.forEach(sharp => {
        const overP = parsePercent(sharp.model_over_percent);
        const underP = parsePercent(sharp.model_under_percent);

        if (overP !== null && underP !== null) {
            totalOverPercent += overP;
            totalUnderPercent += underP;
            validSourceCount++;

            const tieP = parsePercent(sharp.model_tie_percent);
            if (tieP !== null) {
                totalTiePercent += tieP;
            }
        } else {
            console.warn(`[FairOddsCalc] Missing or invalid model_over/under_percent for source: ${sharp.source}`);
        }
    });

    if (validSourceCount === 0) {
        return { over: '+100', under: '+100' };
    }

    let avgOverPercent = totalOverPercent / validSourceCount;
    let avgUnderPercent = totalUnderPercent / validSourceCount;
    // avgTiePercent will also be implicitly divided by validSourceCount if totalTiePercent was summed up correctly.
    // Or, more explicitly:
    let avgTiePercent = (totalTiePercent / validSourceCount) || 0; // Default to 0 if no tie percentages found / validSourceCount is 0 for tie

    let pOverFinal = avgOverPercent;
    let pUnderFinal = avgUnderPercent;

    if (Number.isInteger(line) && avgTiePercent > 0 && !isNaN(avgTiePercent)) {
        const [adjOver, adjUnder] = MetricUtility.adjustForPush(avgOverPercent, avgUnderPercent, avgTiePercent);
        pOverFinal = adjOver;
        pUnderFinal = adjUnder;
    }

    const fairOddsDecimalOver = pOverFinal > 0 ? (1 / pOverFinal) : Infinity;
    const fairOddsDecimalUnder = pUnderFinal > 0 ? (1 / pUnderFinal) : Infinity;

    const fairOddsAmericanOver = decimalToAmerican(fairOddsDecimalOver);
    const fairOddsAmericanUnder = decimalToAmerican(fairOddsDecimalUnder);

    return {
        over: fairOddsAmericanOver,
        under: fairOddsAmericanUnder
    };
}


function calculateOneWayPropFairOdds(sharpProjections: Projection[]): { over: string; under?: string } {
    if (sharpProjections.length === 0) {
        return { over: '+100' }; // Return neutral odds if no data
    }

    const ourSides = sharpProjections.map((projection) =>
        Number(projection.over_odds_decimal)
    ).filter(od => od && od > 1); // Filter out invalid/missing odds

    const oppositeSides = sharpProjections.map((projection) =>
        Number(projection.under_odds_decimal)
    ).filter(od => od && od > 1); // Filter out invalid/missing odds

     // Ensure we have valid odds to average
     if (ourSides.length === 0 || oppositeSides.length === 0) {
         return { over: '+100' };
     }

    const averageOurSide = ourSides.reduce((a, b) => a + b, 0) / ourSides.length;
    const averageOppositeSide = oppositeSides.reduce((a, b) => a + b, 0) / oppositeSides.length;

    const [prob1] = MetricUtility.probitMethod(
        averageOurSide,
        averageOppositeSide,
        { forceDecimal: true }
    );

    // Avoid division by zero if probability is 0
    const fairOddsDecimal = prob1 > 0 ? MetricUtility.impliedOddsToDecimal(prob1) : Infinity;
    const fairOdds = fairOddsDecimal === Infinity ? "+Infinity" : decimalToAmerican(fairOddsDecimal); // Use local decimalToAmerican

    return {
        over: fairOdds
    }
}

export function decimalToAmerican(decimalOdds: number): string {
    const CLAMP_HIGH_STR = "+1000000";
    const CLAMP_LOW_STR = "-1000000";

    if (!isFinite(decimalOdds) || decimalOdds === Infinity) {
        return CLAMP_HIGH_STR; // Prob 0 for this outcome
    }
    if (decimalOdds <= 0) { // Invalid odds, e.g. probability > 1 or negative probability implied
        return CLAMP_HIGH_STR; // Treat as very unlikely
    }
    if (decimalOdds === 1.0) { // Prob 1 for this outcome
        return CLAMP_LOW_STR;
    }
    // For decimal odds very close to 1.0 (e.g., 1.00000001), implies very high negative American odds
    // This check needs to be precise: decimal < 1.000001 means numbers like 1.0000005
    if (decimalOdds > 1.0 && decimalOdds < 1.000001) {
        return CLAMP_LOW_STR;
    }
    // For very high decimal odds (e.g. > 1001, which is +100000 American)
    if (decimalOdds > 1001) { // 1001 decimal is (1001-1)*100 = +100000 American
        return CLAMP_HIGH_STR;
    }

    if (decimalOdds >= 2.0) {
        const american = Math.round((decimalOdds - 1) * 100);
        return `+${american}`;
    } else { // 1.000001 <= decimalOdds < 2.0 (given prior checks)
        const american = Math.round(-100 / (decimalOdds - 1));
        return `${american}`; // Math.round returns number, ensure string for negative
    }
}