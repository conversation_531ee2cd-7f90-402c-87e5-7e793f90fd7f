import proxyList from "./proxyList";

function readProxies(key: string = "default") {
    // Try to get proxies for the specified key, fall back to default if not found
    const lines: string[] = proxyList[key] || proxyList["default"];
    
    if (!lines || lines.length === 0) {
        throw new Error(`No proxies found for key "${key}" and no default proxies available`);
    }
    
    const proxies = lines.map(line => {
        const [ip, port, username, password] = line.split(':');
        if (!ip || !port || !username || !password) {
            throw new Error(`Invalid proxy format: ${line}`);
        }
        return `http://${username}:${password}@${ip}:${port}`;
    });

    return proxies;
}

// Keep track of proxy indices per provider
const proxyIndices: { [key: string]: number } = {};

export function getProxy(key?: string) {
    const provider = key || "default";
    const proxies = readProxies(provider);
    
    // Initialize index for this provider if not exists
    if (!(provider in proxyIndices)) {
        proxyIndices[provider] = 0;
    }
    
    // Get next proxy and increment counter
    const proxy = proxies[proxyIndices[provider] % proxies.length];
    proxyIndices[provider]++;
    
    return proxy;
}