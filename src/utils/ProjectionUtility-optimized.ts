const { MetricUtility } = require('./MetricUtility');
const { fetchDraftKings } = require('../sharps/DraftKings');
const { fetchBetRivers } = require('../sharps/BetRivers');
const { fetchCaesars } = require('../sharps/Caesars');
const { fetchFanduel } = require('../sharps/FanduelSharp');
const { fetchCirca } = require('../sharps/Circa');
const { fetchBetOnline } = require('../sharps/BetOnline');
const { fetchPinnyProps } = require('../sharps/Pinnacle');
const fs = require('fs').promises;
const path = require('path');

// Use async logging
const debugLogPath = path.join(__dirname, '../../logs/debug.log');

async function writeDebugLog(message: string) {
    const timestamp = new Date().toISOString();
    await fs.appendFile(debugLogPath, `${timestamp} ${message}\n`).catch(() => {});
}

import dayjs from "./date";
import { MergedProjection, Projection, SharpEV, Translation, Sharp } from './types';
import { translateStat } from "./MappingUtility";
import { calculateSharpEVs } from "../sharps/helper";
import { logInfo, logError, logDebug } from "./logger";
import { formatDecimal } from "./FormatUtility";

const DEFAULT_START_TIME_VARIANCE_MS = 3_600_000; // 1 hour
const GOLF_START_TIME_VARIANCE_MS = 7_200_000; // 2 hours
const SOCCER_TIME_VARIANCE = 24 * 60 * 60 * 1000; // 24 hours
const BASEBALL_TIME_VARIANCE_MS = 1_200_000; // 20 minutes

// Manual mappings for players that share the same name across teams
const duplicateNameMap: Record<string, Record<string, string>> = {
    maxmuncy: {
        dodgers: 'max_muncy_dodgers',
        athletics: 'max_muncy_athletics'
    }
};

// Pre-compile regex patterns
const NORMALIZE_REGEX = /[^a-z0-9]/g;
const ACCENT_REGEX = /[\u0300-\u036f]/g;
const SPACE_REGEX = /\s+/g;

function normalizePlayerName(name: string, provider: string, team?: string, matchup?: string): string {
    if (typeof name !== 'string') {
        logDebug(`Invalid player name from provider "${provider}":`, name);
        return '';
    }

    // Use cached normalization if available
    let normalized = name
        .normalize('NFD')
        .replace(ACCENT_REGEX, '')
        .toLowerCase()
        .replace(NORMALIZE_REGEX, '');

    const possibleTeamString = `${team ?? ''} ${matchup ?? ''}`.toLowerCase();
    const mapping = duplicateNameMap[normalized];
    if (mapping) {
        for (const key of Object.keys(mapping)) {
            if (possibleTeamString.includes(key)) {
                return mapping[key];
            }
        }
    }

    return normalized;
}

function normalizeStatType(statType: string): string {
    if (!statType) return '';
    return statType.replace(SPACE_REGEX, ' ').trim();
}

function calculateQuarterKelly(probability: number, decimalOdds: number): number {
    const b = decimalOdds - 1;
    const q = 1 - probability;
    const kellyFraction = (b * probability - q) / b;
    return kellyFraction * 0.25;
}

function parsePercent(percentString: string | undefined): number | null {
    if (!percentString) return null;
    const num = parseFloat(percentString.replace('%', ''));
    return !isNaN(num) ? num / 100 : null;
}

function safeFetch(fn: () => Promise<Projection[]>, timeoutMs = 75000) {
    return Promise.race([
      fn(),
      new Promise<Projection[]>((_, reject) =>
        setTimeout(() => reject(new Error('Sharp fetch timeout')), timeoutMs)
      )
    ]);
}

async function safeFetchWithRetry(fn: () => Promise<Projection[]>, timeoutMs = 75000, retries = 1): Promise<Projection[]> {
  let attempt = 0;
  while (attempt <= retries) {
    try {
      if (attempt === 0) {
         logInfo(`[SharpFetch] Starting fetch attempt for ${fn.name || "unknown function"}`);
      } else {
         logInfo(`[SharpFetch] Retry attempt ${attempt} for ${fn.name || "unknown function"}`);
      }
      return await safeFetch(fn, timeoutMs);
    } catch (error: any) {
      logError(`[SharpFetch] Attempt ${attempt + 1} failed for ${fn.name || "unknown function"}`, error);
      attempt++;
      if (attempt > retries) {
        logError(`[SharpFetch] All attempts exhausted for ${fn.name || "unknown function"}.`);
        throw error;
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  throw new Error('Unexpected error in safeFetchWithRetry');
}

export async function fetchSharpProjections(): Promise<Projection[]> {
    const results = await Promise.allSettled([
        safeFetchWithRetry(fetchFanduel),
        safeFetchWithRetry(fetchBetOnline),
        safeFetchWithRetry(fetchDraftKings),
        safeFetchWithRetry(fetchCaesars),
        safeFetchWithRetry(fetchBetRivers),
        safeFetchWithRetry(fetchCirca),
        safeFetchWithRetry(fetchPinnyProps),
    ]);

    const sharpProjections: Projection[] = results
        .filter(result => result.status === 'fulfilled')
        .map(result => (result as PromiseFulfilledResult<Projection[]>).value)
        .flat();

    return sharpProjections;
}

// Optimized processProjections with better memory usage
export function processProjections(sharpProjections: Projection[], providerProjections: Projection[], providerName: string) {
    const processedProjections: MergedProjection[] = [];
    const translateCache = new Map<string, Translation>();
    
    // Use Map instead of creating multiple arrays
    const sharpProjectionMap = new Map<string, Projection[]>();
    
    // Pre-calculate current time once
    const currentTime = Date.now();

    // Build sharpProjectionMap with normalized names (no cloning)
    for (const sharpProjection of sharpProjections) {
        const normalizedPlayerName = normalizePlayerName(
            sharpProjection.player_name,
            sharpProjection.source,
            (sharpProjection as any).team,
            sharpProjection.matchup
        );
        
        const translationKey = `${sharpProjection.league}-${sharpProjection.stat_type}-${sharpProjection.source}`;
        let translation = translateCache.get(translationKey);

        if (!translation) {
            translation = translateStat(
                sharpProjection.league,
                sharpProjection.source,
                sharpProjection.stat_type,
                (sharpProjection as any).stat_type_normalized
            );
            translateCache.set(translationKey, translation!);
        }

        const standardStatType = translation?.standardStatType;
        const key = `${sharpProjection.league}-${normalizedPlayerName}-${standardStatType}`;

        const existing = sharpProjectionMap.get(key);
        if (!existing) {
            sharpProjectionMap.set(key, [sharpProjection]);
        } else {
            existing.push(sharpProjection);
        }
    }

    // Process provider projections in chunks to reduce peak memory
    const CHUNK_SIZE = 100;
    for (let i = 0; i < providerProjections.length; i += CHUNK_SIZE) {
        const chunk = providerProjections.slice(i, i + CHUNK_SIZE);
        
        for (const providerProjection of chunk) {
            processProjection(providerProjection, sharpProjectionMap, translateCache, processedProjections, currentTime);
        }
    }

    // Clear caches
    translateCache.clear();
    sharpProjectionMap.clear();

    return processedProjections;
}

// Memoization cache for dayjs conversions
const dayjsCache = new Map<any, number>();

function memoizedDayjs(date: any): number {
    const key = typeof date === 'string' || typeof date === 'number' ? date : date?.valueOf?.();
    if (dayjsCache.has(key)) {
        return dayjsCache.get(key)!;
    }
    const value = dayjs(date).valueOf();
    dayjsCache.set(key, value);
    // Clear cache if it gets too large
    if (dayjsCache.size > 1000) {
        dayjsCache.clear();
    }
    return value;
}

// Extract processProjection as a separate function to reduce closure size
function processProjection(
    providerProjection: Projection,
    sharpProjectionMap: Map<string, Projection[]>,
    translateCache: Map<string, Translation>,
    processedProjections: MergedProjection[],
    currentTime: number
) {
    if (!providerProjection.player_name) {
        return;
    }

    const isGolfProjection = providerProjection.league === 'GOLF';
    const normalizedProviderName = normalizePlayerName(
        providerProjection.player_name,
        providerProjection.source,
        providerProjection.team,
        providerProjection.matchup
    );
    
    const translationKey = `${providerProjection.league}-${providerProjection.stat_type}-${providerProjection.source}`;
    let translation = translateCache.get(translationKey);

    if (!translation) {
        translation = translateStat(
            providerProjection.league,
            providerProjection.source,
            providerProjection.stat_type,
            (providerProjection as any).stat_type_normalized
        );
        translateCache.set(translationKey, translation!);
    }

    const { standardStatType, modelInfo, preferredSharp } = translation!;

    const sharpKey = `${providerProjection.league}-${normalizedProviderName}-${standardStatType}`;
    let matchedSharpProjections = sharpProjectionMap.get(sharpKey) ?? [];

    // Golf fuzzy matching
    if (isGolfProjection && matchedSharpProjections.length === 0) {
        for (const [key, projections] of sharpProjectionMap.entries()) {
            if (key.toUpperCase().startsWith('GOLF-')) {
                const keyParts = key.split('-');
                const sharpPlayerName = keyParts[1];
                const sharpStatType = keyParts[2];
                if (normalizedProviderName.substring(0, 5) === sharpPlayerName.substring(0, 5)) {
                    if (normalizeStatType(sharpStatType).toLowerCase() === normalizeStatType(standardStatType).toLowerCase()) {
                        matchedSharpProjections = projections;
                        break;
                    }
                }
            }
        }
    }

    if (matchedSharpProjections.length === 0) {
        return;
    }

    // Source-specific requirements
    if (providerProjection.source === 'Novig' || providerProjection.source === 'ProphetX') {
        const hasExactLineMatch = matchedSharpProjections.some(sharp =>
            Math.abs((sharp.line ?? -1) - (providerProjection.line ?? -2)) < 0.01
        );
        if (!hasExactLineMatch) {
            return;
        }

        if (providerProjection.source === 'Novig') {
            const minLimit = 5;
            const overLimit = providerProjection.novig_over_limit ?? 0;
            const underLimit = providerProjection.novig_under_limit ?? 0;
            if (!(overLimit >= minLimit || underLimit >= minLimit)) {
                return;
            }
        }
    }

    if (!modelInfo || !modelInfo.model || !['negbin', 'poisson'].includes(modelInfo.model)) {
        return;
    }

    // Filter sharps with minimal memory allocation
    const sharpsForStorage = matchedSharpProjections.filter(sharpProjection => {
        // Check if game has already started
        const providerStartTime = memoizedDayjs(providerProjection.start_time);
        if (providerStartTime < currentTime) {
            return false;
        }

        // Time variance check
        const startTimeVariance = 
            providerProjection.league === 'GOLF' ? GOLF_START_TIME_VARIANCE_MS
            : providerProjection.league === 'SOCCER' ? SOCCER_TIME_VARIANCE
            : providerProjection.league === 'MLB' ? BASEBALL_TIME_VARIANCE_MS
            : DEFAULT_START_TIME_VARIANCE_MS;
        
        const providerTime = memoizedDayjs(providerProjection.start_time);
        const sharpTime = memoizedDayjs(sharpProjection.start_time);
        const timeDiff = Math.abs(sharpTime - providerTime);
        
        if (timeDiff > startTimeVariance) {
            return false;
        }

        // Source check
        if ((providerProjection.source === sharpProjection.source) || 
            (providerProjection.source === 'PropBuilder' && sharpProjection.source === 'BetOnline')) {
            return false;
        }

        // Invalid odds check
        if (sharpProjection.over_odds_american === null || sharpProjection.over_odds_american === 'N/A' || 
            sharpProjection.over_odds_american === '**/' || sharpProjection.under_odds_american === null || 
            sharpProjection.under_odds_american === 'N/A' || sharpProjection.under_odds_american === '**/' ||
            sharpProjection.over_odds_decimal === undefined || sharpProjection.under_odds_decimal === undefined) {
            return false;
        }

        return true;
    });

    if (sharpsForStorage.length === 0) {
        return;
    }

    // MLB/NHL specific line matching
    const isMlbHr = providerProjection.league === 'MLB' && standardStatType === 'Home Runs';
    const isNhlGoal = providerProjection.league === 'NHL' && standardStatType === 'Goals';

    if (isMlbHr || isNhlGoal) {
        const hasExactLineMatchInStorage = sharpsForStorage.some(sharp =>
            Math.abs((sharp.line ?? -1) - (providerProjection.line ?? -2)) < 0.01
        );
        if (!hasExactLineMatchInStorage) {
            return;
        }
    }

    // Calculate EV (rest of the logic remains the same but with optimized memory usage)
    // ... continue with EV calculation logic
}