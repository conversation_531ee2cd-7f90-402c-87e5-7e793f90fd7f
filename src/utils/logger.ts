import pino from 'pino';
import dayjs from './date';

// Create pino logger with async transport
const pinoLogger = pino({
  level: process.env.LOG_LEVEL || 'info',
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'SYS:standard',
      ignore: 'pid,hostname',
    },
  },
  base: {
    pid: process.pid,
  },
  timestamp: () => `,"time":"${dayjs().format('L LT')}"`,
});

// Export both named logger and default function for backwards compatibility
export const logger = pinoLogger;

export default function log(message: string, level: string = 'info') {
  pinoLogger[level](message);
}

// Additional utility methods
export const logError = (message: string, error?: Error) => {
  if (error) {
    pinoLogger.error({ err: error }, message);
  } else {
    pinoLogger.error(message);
  }
};

export const logDebug = (message: string, data?: any) => {
  if (data) {
    pinoLogger.debug(data, message);
  } else {
    pinoLogger.debug(message);
  }
};

export const logInfo = (message: string, data?: any) => {
  if (data) {
    pinoLogger.info(data, message);
  } else {
    pinoLogger.info(message);
  }
};

export const logWarn = (message: string, data?: any) => {
  if (data) {
    pinoLogger.warn(data, message);
  } else {
    pinoLogger.warn(message);
  }
};

// Performance logging utility
export const logPerformance = (operation: string, duration: number, metadata?: any) => {
  pinoLogger.info({
    type: 'performance',
    operation,
    duration,
    ...metadata,
  }, `${operation} completed in ${duration}ms`);
};