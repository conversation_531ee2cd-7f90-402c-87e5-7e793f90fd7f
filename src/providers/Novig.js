const { RequestManager } = require('../services/RequestManager');
const { logInfo, logError, logWarn } = require('../utils/logger');

const requestManager = RequestManager.getInstance();

const EVENTS_QUERY = `query Home_Query($order_by_event: [event_order_by!], $where_event: event_bool_exp, $limit_count: Int!) {
  event(where: $where_event, order_by: $order_by_event, limit: $limit_count) {
    id
    __typename
  }
}`;

const BATCH_EVENT_DETAILS_QUERY = `query BatchEventDetails_Query($eventIds: [uuid!]) {
  event(where: {id: {_in: $eventIds}}) {
    id
    status
    ...SimplifiedMarketSelector_Frag
    __typename
  }
}

fragment SimplifiedOutcome_Frag on outcome {
  id
  description
  available
  orders(where: {status: {_eq: "OPEN"}, currency: {_eq: "CASH"}}) {
    qty
    price
    __typename
  }
  market {
    event {
      game {
        scheduled_start
        awayTeam {
          symbol
          __typename
        }
        homeTeam {
          symbol
          __typename
        }
        __typename
      }
      __typename
    }
    __typename
  }
  __typename
}

fragment SimplifiedMarketSelector_Frag on event {
  id
  markets(where: {
    _and: [
      { type: { _nin: ["SPREAD", "TOTAL", "MONEY"] } },
      { status: { _eq: "OPEN" } },
      { outcomes: { available: { _is_null: false } } }
    ]
  }) {
    id
    strike
    type
    status
    outcomes(where: { description: { _regex: "^(Over|Under)" }}) {
      id
      ...SimplifiedOutcome_Frag
      __typename
    }
    player {
      id
      full_name
      __typename
    }
    __typename
  }
  __typename
}`;

function decimalToAmerican(decimalOdds) {
    if (!isFinite(decimalOdds) || decimalOdds <= 1) {
        return null;
    }
    if (decimalOdds >= 2) {
        return `+${Math.round((decimalOdds - 1) * 100)}`;
    } else {
        const americanOddsValue = Math.round(-100 / (decimalOdds - 1));
        return `${americanOddsValue}`;
    }
}

function extractProjections(events, league) {
    const projections = [];
    
    for (const event of events) {
        if (!event || event.status !== 'OPEN_PREGAME') continue;

        for (const market of event.markets || []) {
            if (!market.player) continue;

            const overOutcome = market.outcomes.find(o => o.description.startsWith('Over'));
            const underOutcome = market.outcomes.find(o => o.description.startsWith('Under'));
            
            if (!overOutcome || !underOutcome) continue;

            const overOddsDecimal = 1 / (overOutcome.available ?? Infinity);
            const underOddsDecimal = 1 / (underOutcome.available ?? Infinity);

            const tolerance = 1e-6;
            const overLimit = overOutcome.orders?.reduce((acc, order) => {
                if (Math.abs(order.price - overOutcome.available) < tolerance) {
                    return acc + order.qty;
                }
                return acc;
            }, 0) || null;

            const underLimit = underOutcome.orders?.reduce((acc, order) => {
                if (Math.abs(order.price - underOutcome.available) < tolerance) {
                    return acc + order.qty;
                }
                return acc;
            }, 0) || null;

            const gameInfo = overOutcome.market?.event?.game || underOutcome.market?.event?.game;
            const awaySymbol = gameInfo?.awayTeam?.symbol || 'AWAY';
            const homeSymbol = gameInfo?.homeTeam?.symbol || 'HOME';
            const scheduled_start = gameInfo?.scheduled_start;

            projections.push({
                proj_id: market.id,
                player_name: market.player.full_name,
                stat_type: market.type,
                line: market.strike,
                over_odds_american: decimalToAmerican(overOddsDecimal),
                under_odds_american: decimalToAmerican(underOddsDecimal),
                over_odds_decimal: overOddsDecimal,
                under_odds_decimal: underOddsDecimal,
                over_limit: overLimit,
                under_limit: underLimit,
                league: league,
                matchup: `${awaySymbol} vs. ${homeSymbol}`,
                start_time: new Date(scheduled_start),
                source: 'Novig'
            });
        }
    }

    return projections;
}

async function fetchLeagueProjections(league) {
    const initialQuery = {
        operationName: "Home_Query",
        variables: {
            where_event: {
                _and: [
                    {
                        _or: [
                            {
                                _and: [
                                    { status: { _eq: "OPEN_PREGAME" } },
                                    { is_visible_pregame: { _eq: true } }
                                ]
                            }
                        ]
                    },
                    { game: { league: { _eq: league } } }
                ]
            },
            order_by_event: [
                { game: { scheduled_start: "asc" } },
                { game: { id: "asc" } }
            ],
            limit_count: 10000
        },
        query: EVENTS_QUERY
    };

    try {
        // Fetch all event IDs
        const eventsResponse = await requestManager.fetch('novig', 'https://gql.novig.us/v1/graphql', {
            method: 'POST',
            headers: {
                'content-type': 'application/json',
                'user-agent': 'Novig/230 CFNetwork/3826.400.120 Darwin/24.3.0',
                'accept-language': 'en-US,en;q=0.9'
            },
            body: JSON.stringify(initialQuery),
            retries: 1
        });

        const eventsData = await eventsResponse.json();
        
        if (eventsData.errors) {
            logError(`[NOVIG] GraphQL errors fetching ${league} events`, eventsData.errors);
            return [];
        }

        const eventIds = eventsData.data?.event?.map(e => e.id) || [];
        
        if (eventIds.length === 0) {
            return [];
        }

        logInfo(`[NOVIG] Found ${eventIds.length} events for ${league}`);

        // Batch fetch event details
        const allProjections = [];
        const batchSize = 20; // Reduced batch size from 50 to 20

        for (let i = 0; i < eventIds.length; i += batchSize) {
            const batchEventIds = eventIds.slice(i, i + batchSize);
            
            const batchQuery = {
                operationName: "BatchEventDetails_Query",
                variables: { eventIds: batchEventIds },
                query: BATCH_EVENT_DETAILS_QUERY
            };

            try {
                const batchResponse = await requestManager.fetch('novig', 'https://gql.novig.us/v1/graphql', {
                    method: 'POST',
                    headers: {
                        'content-type': 'application/json',
                        'user-agent': 'Novig/230 CFNetwork/3826.400.120 Darwin/24.3.0',
                        'accept-language': 'en-US,en;q=0.9'
                    },
                    body: JSON.stringify(batchQuery),
                    timeout: 30000
                });

                const batchData = await batchResponse.json();
                
                if (batchData.errors) {
                    logWarn(`[NOVIG] GraphQL errors in batch ${Math.floor(i / batchSize) + 1}`, batchData.errors);
                }

                if (batchData.data?.event) {
                    const projections = extractProjections(batchData.data.event, league);
                    allProjections.push(...projections);
                }
            } catch (error) {
                logError(`[NOVIG] Error fetching batch ${Math.floor(i / batchSize) + 1}`, error);
            }
        }

        return allProjections;

    } catch (error) {
        logError(`[NOVIG] Error fetching ${league} projections`, error);
        return [];
    }
}

async function fetchNovig() {
    const SUPPORTED_LEAGUES = ['NBA', 'WNBA', 'NFL', 'MLB'];
    let allProjections = [];

    // Use Promise.all to fetch all leagues in parallel
    const leaguePromises = SUPPORTED_LEAGUES.map(league => 
        fetchLeagueProjections(league).catch(error => {
            logError(`[NOVIG] Failed to fetch ${league}`, error);
            return [];
        })
    );

    const leagueResults = await Promise.all(leaguePromises);
    
    for (const projections of leagueResults) {
        allProjections = allProjections.concat(projections);
    }

    logInfo(`[NOVIG] Total projections fetched: ${allProjections.length}`);
    return allProjections;
}

module.exports = { fetchNovig };