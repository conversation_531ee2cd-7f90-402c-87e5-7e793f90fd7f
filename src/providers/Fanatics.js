const fs = require('fs').promises;
const path = require('path');
const { RequestManager } = require('../services/RequestManager');

const leagueMap = {
  MLB: {
    url: "https://content.1.betfanatics.com/page/league/1257?channel=AMELCO_TN_MASTER&segment=AMELCO_TN&stateCode=TN&tab=Schedule&dma=693",
    cardPacks: ['Batter Props', 'Pitcher Props']
  },
  NFL: {
    url: "https://content.1.betfanatics.com/page/league/364897?channel=AMELCO_TN_MASTER&segment=AMELCO_TN&stateCode=TN&tab=schedule&dma=693",
    cardPacks: ['Passing Props', 'Receiving Props', 'Rushing Props']
  },
  CFB: {
    url: "https://content.1.betfanatics.com/page/league/317296?channel=AMELCO_TN_MASTER&segment=AMELCO_TN&stateCode=TN&tab=schedule&dma=693",
    cardPacks: ['Passing Props', 'Receiving Props', 'Rushing Props']
  },
  NHL: {
    url: 'https://content.1.betfanatics.com/page/league/212?channel=AMELCO_TN_MASTER&segment=AMELCO_TN&stateCode=TN&tab=Schedule&dma=693',
    cardPacks: ['Player Props']
  },
  NBA: {
    url: 'https://content.1.betfanatics.com/page/league/417218?channel=AMELCO_TN_MASTER&segment=AMELCO_TN&stateCode=TN&tab=Schedule&dma=693',
    cardPacks: ['Player Points', 'Player Threes', 'Player Assists ', 'Player Assists', 'Player Rebounds', 'Player Combos', 'Player Defense']
  },
  WNBA: {
    url: 'https://content.1.betfanatics.com/page/league/1189?channel=AMELCO_TN_MASTER&segment=AMELCO_TN&stateCode=TN&tab=Schedule&dma=693',
    cardPacks: ['Player Points', 'Player Threes', 'Player Assists ', 'Player Assists', 'Player Rebounds', 'Player Combos', 'Player Defense']
  },

  // Add other leagues here as needed
};

const headers = {
  'content-type': 'application/json',
  'accept': 'application/json',
  'app-version': '4.1.2',
  'accept-charset': 'UTF-8',
  'accept-language': 'en-US,en;q=0.9',
  'accept-encoding': 'gzip, deflate, br',
  'platform': 'ios',
  'content-length': '0',
  'ld-device-id': '2C3B4529-9ED3-4E2B-8BC0-2A8E2ACA15A3',
  'user-agent': 'BetFanaticsApplication/4.1.2 (iOS)'
};

async function fetchWithProxy(url, options = {}) {
  const requestManager = RequestManager.getInstance();
  
  const response = await requestManager.fetch('fanatics', url, {
    method: 'GET',
    headers: options.headers || {},
    timeout: 15000,
    retries: 2
  });
  
  if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
  return await response.json();
}

async function processLeagueData(league) {
  const leagueInfo = leagueMap[league];
  if (!leagueInfo) throw new Error(`No information found for league: ${league}`);
  
  const data = await fetchWithProxy(leagueInfo.url, { headers });
  
  const events = [];
  if (data.cardPack && data.cardPack.cards) {
    data.cardPack.cards.forEach(card => {
      if (card.data && card.data.event && card.data.event.state === "ACTIVE") {
        events.push({
          eventId: card.data.event.id,
          matchup: card.data.event.name,
          start_time: new Date(card.data.event.eventTime).toISOString()
        });
      }
    });
  }
  return events;
}

function processProjections(propData, eventInfo, league) {
  const projections = [];
  if (propData.cards) {
    propData.cards.forEach(card => {
      if (card.markets && card.markets.length > 0) {
        card.markets.forEach(market => {
          if (market.selection && market.selection.length === 2) {
            const overSelection = market.selection.find(s => s.type === "Over");
            const underSelection = market.selection.find(s => s.type === "Under");
            if (overSelection && underSelection && 
                overSelection.state === "ACTIVE" && underSelection.state === "ACTIVE") {
              const nameParts = market.name.split(' - ');
              if (nameParts.length === 2) {
                const projection = {
                  proj_id: `${overSelection.id}_${underSelection.id}`,
                  league: league,
                  player_name: nameParts[0].trim(),
                  stat_type: nameParts[1].trim(),
                  line: parseFloat(overSelection.name.split(' ')[1]),
                  over_odds_american: overSelection.moneylineOdds > 0 ? `+${overSelection.moneylineOdds}` : overSelection.moneylineOdds,
                  under_odds_american: underSelection.moneylineOdds,
                  over_odds_decimal: parseFloat(overSelection.decimalOdds),
                  under_odds_decimal: parseFloat(underSelection.decimalOdds),
                  matchup: eventInfo.matchup,
                  start_time: eventInfo.start_time,
                  source: "Fanatics"
                };

                // Check if any value in the projection is null
                const hasNullValue = Object.values(projection).some(value => value === null);

                // Only add the projection if it doesn't have any null values
                if (!hasNullValue) {
                  projections.push(projection);
                }
              }
            }
          }
        });
      }
    });
  }
  return projections;
}

async function fetchEventDetails(event, cardPackTitles) {
  const url = `https://content.1.betfanatics.com/page/event/${event.eventId}?channel=AMELCO_TN_MASTER&segment=AMELCO_TN&stateCode=TN&isTournament=false&dma=693`;
  const data = await fetchWithProxy(url, { headers });
  const cardPackIds = {};
  if (data.tabs) {
    data.tabs.forEach(tab => {
      if (cardPackTitles.includes(tab.title)) {
        cardPackIds[tab.title] = tab.cardPackId;
      }
    });
  }
  return { event, cardPackIds };
}

async function fetchAndProcessPropData(cardPackId, event, league) {
  const url = `https://content.1.betfanatics.com/cardpack/${cardPackId}?channel=AMELCO_TN_MASTER&segment=AMELCO_TN&stateCode=TN`;
  const propData = await fetchWithProxy(url, { headers });
  return processProjections(propData, event, league);
}

async function fetchLeagueData(league) {
  const events = await processLeagueData(league);
  const cardPackTitles = leagueMap[league].cardPacks;

  const eventDetailsPromises = events.map(event => fetchEventDetails(event, cardPackTitles));
  const eventDetailsResults = await Promise.allSettled(eventDetailsPromises);

  const propDataPromises = eventDetailsResults
    .filter(result => result.status === 'fulfilled')
    .flatMap(result => {
      const { event, cardPackIds } = result.value;
      return Object.values(cardPackIds).map(id => fetchAndProcessPropData(id, event, league));
    });

  const propDataResults = await Promise.allSettled(propDataPromises);

  return propDataResults
    .filter(result => result.status === 'fulfilled')
    .flatMap(result => result.value);
}

async function fetchFanatics() {
  const leaguePromises = Object.keys(leagueMap).map(fetchLeagueData);
  const leagueResults = await Promise.allSettled(leaguePromises);

  return leagueResults
    .filter(result => result.status === 'fulfilled')
    .flatMap(result => result.value);
}

module.exports = { fetchFanatics };