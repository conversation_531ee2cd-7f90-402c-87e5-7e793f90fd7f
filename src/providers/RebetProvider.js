// Rebet.js
const { RequestManager } = require('../services/RequestManager');

function decimalToAmerican(decimalOdds) {
    if (decimalOdds >= 2) {
        return `+${Math.round((decimalOdds - 1) * 100)}`;
    } else {
        return `-${Math.round(100 / (decimalOdds - 1))}`;
    }
}

function extractProjections(events) {
    const projections = [];

    for (const event of events) {
        if (event.status !== "not_started") continue;

        const competitors = event.competitors.competitor;
        const homeTeam = competitors.find(c => c.qualifier === "home")?.abbreviation;
        const awayTeam = competitors.find(c => c.qualifier === "away")?.abbreviation;
        const matchup = `${awayTeam} vs. ${homeTeam}`;

        if (event.odds?.market) {
            for (const market of event.odds.market) {
                if (!market.specifiers?.includes("player=sr:player")) continue;

                const overOutcome = market.outcome?.find(o => o.name.startsWith("over"));
                const underOutcome = market.outcome?.find(o => o.name.startsWith("under"));
                if (!overOutcome || !underOutcome) continue;

                const line = parseFloat(overOutcome.name.split(" ")[1]);
                if (isNaN(line)) continue;

                const [lastName, firstName] = market.player_name.split(", ");
                const playerName = `${firstName} ${lastName}`;
                const overOdds = parseFloat(overOutcome.odds);
                const underOdds = parseFloat(underOutcome.odds);
                const statType = market.name.split(' ').slice(1).join(' ').split(' (')[0];
                const startTime = new Date(event.start_time);

                projections.push({
                    proj_id: market.m_id,
                    league: event.league_name,
                    player_name: playerName,
                    stat_type: statType,
                    line: line,
                    over_odds_american: decimalToAmerican(overOdds),
                    under_odds_american: decimalToAmerican(underOdds),
                    over_odds_decimal: overOdds,
                    under_odds_decimal: underOdds,
                    matchup: matchup,
                    start_time: startTime,
                    source: 'Rebet'
                });
            }
        }
    }

    return projections;
}

async function fetchGamesData(leagueName) {
    const tournament_id = leagues[leagueName];
    if (!tournament_id) {
        throw new Error(`Unknown league: ${leagueName}`);
    }

    const requestBody = {
        tournament_id,
        game_type: 1,
        custom_filter: false
    };

    try {
        const requestManager = RequestManager.getInstance();
        const response = await requestManager.fetch('rebet', 'https://api.rebet.app/prod/sportsbook-v3/load-sportsbook-data-v3', {
            method: 'POST',
            headers: {
                'host': 'api.rebet.app',
                'accept': 'application/json, text/plain, */*',
                'content-type': 'application/json',
                'accept-encoding': 'gzip, deflate, br',
                'user-agent': 'rebetMobileApp/1034 CFNetwork/1568.300.101 Darwin/24.2.0',
                'accept-language': 'en-US,en;q=0.9'
            },
            body: JSON.stringify(requestBody),
            timeout: 30000,
            retries: 3
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const responseData = await response.json();
        let projections = [];
        projections = extractProjections(responseData.data.events);

        return projections;
    } catch (error) {
        throw new Error(`[REBET] ${error.message}`);
    }
}

async function fetchRebet() {
    try {
        let allProjections = [];

        for (const [leagueName, _] of Object.entries(leagues)) {
            // Try up to 3 different Rebet proxies for each league
            for (let attempt = 0; attempt < 3; attempt++) {
                try {
                    const projections = await fetchGamesData(leagueName);
                    allProjections = allProjections.concat(projections);
                    break;
                } catch (error) {
                    console.error(`[REBET] Failed attempt ${attempt + 1}/3 for ${leagueName} - ${error.message}`);
                    if (attempt === 2) {
                        console.error(`[REBET] All attempts failed for ${leagueName}`);
                    }
                    continue;
                }
            }
        }

        return allProjections;
    } catch (error) {
        console.error(`[REBET] ${error.message}`);
        throw error;
    }
}

const leagues = {
    // 'NFL': 'sr:tournament:31',
    'NBA': 'sr:tournament:132',
    // CFB': 'sr:tournament:27653',
    'NCAAB': 'sr:tournament:648',
    'NHL': 'sr:tournament:234',
    'MLB': 'sr:tournament:109'
};

module.exports = { fetchRebet };