const { readFile, writeFile } = require('fs/promises');
const { RequestManager } = require('../services/RequestManager');
const path = require('path');
const querystring = require('querystring');

const requestManager = RequestManager.getInstance();

const LEAGUES = ['NFL', 'MLB', 'NHL', 'UFC', 'NBA', 'WNBA', 'CFB', 'CBB']; // 'PGA' is not supported yet
const API_URL = 'https://api.fantasy.betr.app/graphql';
const HEADERS = {
  'Host': 'api.fantasy.betr.app',
  'Content-Type': 'application/json',
  'accept': 'application/graphql-response+json, application/graphql+json, application/json, text/event-stream, multipart/mixed',
  'channel': 'IOS',
  'fantasy-application-version': '3.16.17',
  'fantasy-api-version': '6.0',
  'accept-language': 'en-US,en;q=0.9',
  'accept-encoding': 'gzip, deflate, br',
  'user-agent': 'Betr/4374 CFNetwork/1568.300.91 Darwin/24.2.0',
};

async function getAccessToken() {
  try {
    const url = 'https://account.betr.app/realms/betr/protocol/openid-connect/token';
    const body = querystring.stringify({
      grant_type: 'refresh_token',
      refresh_token: 'eyJhbGciOiJIUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJkY2RlOTgyOC0zYWMzLTRkZjItOTZhMy1hYTU2Yjk4ZTU4NWYifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ecEnF9MgxYi_rm40rmXWuWo2gvz7HyM-xSB1tLw3DcI',
      client_id: 'betr-rn'
    });

    const response = await requestManager.fetch('betr', url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Betr/4103 CFNetwork/1474 Darwin/23.0.0'
      },
      body: body,
      timeout: 15000,
      retries: 2
    });
    const data = await response.json();

    if (data.access_token) {
      return data.access_token;
    } else {
      console.error('[BETR] Error retrieving access token:', data);
      throw new Error('Failed to retrieve access token');
    }
  } catch (error) {
    console.error('[BETR] Request failed when getting access token:', error);
    throw error;
  }
}

async function getProxies() {
  try {
    const proxiesData = await readFile(PROXIES_PATH, 'utf-8');
    const proxies = proxiesData.split('\n').map(proxy => proxy.trim()).filter(proxy => proxy);
    if (proxies.length === 0) throw new Error('No proxies available.');
    return proxies;
  } catch (error) {
    console.error('Error reading proxies file:', error);
    throw error;
  }
}

async function getUpcomingEvents(league, eventIds) {
  // Get a fresh access token
  const accessToken = await getAccessToken();
  const headers = {
    ...HEADERS,
    'authorization': accessToken
  };

  const body = JSON.stringify({
    operationName: 'UpcomingEventsConditionalWithPlayers',
    query: `query UpcomingEventsConditionalWithPlayers($league: League!, $withPlayers: Boolean!) {
      getUpcomingEventsV2(league: $league) {
        id
        ...EventInfoData
        ... on TeamTournamentEvent {
          teams {
            ...TeamInfo
            __typename
          }
          __typename
        }
        ... on TeamVersusEvent {
          teams {
            ...TeamInfo
            __typename
          }
          __typename
        }
        ... on IndividualTournamentEvent {
          players @include(if: $withPlayers) {
            ...PlayerInfoWithProjections
            __typename
          }
          __typename
        }
        ... on IndividualVersusEvent {
          players @include(if: $withPlayers) {
            ...PlayerInfoWithProjections
            __typename
          }
          __typename
        }
        __typename
      }
    }
    fragment EventInfoData on EventV2 {
      id
      date
      status
      sport
      league
      competitionType
      playerStructure
      venueDetails {
        name
        city
        country
        __typename
      }
      headerImage
      attributes {
        key
        value
        __typename
      }
      name
      icon
      dedicated
      __typename
    }
    fragment TeamInfo on Team {
      id
      name
      league
      sport
      icon
      color
      largeIcon
      __typename
    }
    fragment PlayerInfoWithProjections on Player {
      ...PlayerInfo
      projections {
        ...PlayerProjection
        __typename
      }
      __typename
    }
    fragment PlayerInfo on Player {
      id
      firstName
      lastName
      icon
      position
      jerseyNumber
      attributes {
        key
        value
        __typename
      }
      __typename
    }
    fragment PlayerProjection on Projection {
      marketId
      marketStatus
      isLive
      type
      label
      name
      key
      order
      value
      nonRegularPercentage
      nonRegularValue
      allowedOptions {
        marketOptionId
        outcome
        __typename
      }
      __typename
    }`,
    variables: {
      league,
      withPlayers: false,
    },
  });

  try {
    const response = await requestManager.fetch('betr', API_URL, {
      method: 'POST',
      headers: headers,
      body,
      timeout: 15000,
      retries: 2
    });

    const responseData = await response.json();
    if (responseData?.data?.getUpcomingEventsV2?.length > 0) {
      const ids = responseData.data.getUpcomingEventsV2.map(event => event.id);
      eventIds.push(...ids);
      //console.log(`Event IDs for ${league}:`, ids);
    } else {
      console.log(`[BETR] No upcoming events found for ${league}.`);
    }
  } catch (error) {
    console.error(`[BETR] Error fetching upcoming events for ${league}:`, error);
  }
}

async function getAllUpcomingEvents() {
  try {
    const eventIds = [];
    await Promise.all(LEAGUES.map((league) => {
      return getUpcomingEvents(league, eventIds);
    }));
    console.log(`[BETR] Found ${eventIds.length} event IDs`);
    return eventIds;
  } catch (error) {
    console.error('[BETR] Error fetching all upcoming events:', error);
    return [];
  }
}

async function getEventsByIds(ids) {
  // Get a fresh access token
  const accessToken = await getAccessToken();
  const headers = {
    ...HEADERS,
    'authorization': accessToken
  };

  const body = JSON.stringify({
    operationName: 'EventsInfo',
    query: `query EventsInfo($ids: [String!]!) {
      getEventsByIdsV2(ids: $ids) {
        ...EventInfoData
        ... on TeamTournamentEvent {
          teams {
            ...TeamInfoWithPlayers
            __typename
          }
          __typename
        }
        ... on TeamVersusEvent {
          teams {
            ...TeamInfoWithPlayers
            __typename
          }
          __typename
        }
        ... on IndividualTournamentEvent {
          players {
            ...PlayerInfoWithProjections
            __typename
          }
          __typename
        }
        ... on IndividualVersusEvent {
          players {
            ...PlayerInfoWithProjections
            __typename
          }
          __typename
        }
        __typename
      }
    }
    fragment EventInfoData on EventV2 {
      id
      date
      status
      sport
      league
      competitionType
      playerStructure
      venueDetails {
        name
        city
        country
        __typename
      }
      headerImage
      attributes {
        key
        value
        __typename
      }
      name
      icon
      dedicated
      __typename
    }
    fragment TeamInfoWithPlayers on Team {
      ...TeamInfo
      players {
        ...PlayerInfoWithProjections
        __typename
      }
      __typename
    }
    fragment TeamInfo on Team {
      id
      name
      league
      sport
      icon
      color
      largeIcon
      __typename
    }
    fragment PlayerInfoWithProjections on Player {
      ...PlayerInfo
      projections {
        ...PlayerProjection
        __typename
      }
      __typename
    }
    fragment PlayerInfo on Player {
      id
      firstName
      lastName
      icon
      position
      jerseyNumber
      attributes {
        key
        value
        __typename
      }
      __typename
    }
    fragment PlayerProjection on Projection {
      marketId
      marketStatus
      isLive
      type
      label
      name
      key
      order
      value
      nonRegularPercentage
      nonRegularValue
      allowedOptions {
        marketOptionId
        outcome
        __typename
      }
      __typename
    }`,
    variables: {
      ids,
    },
  });

  try {

    const response = await requestManager.fetch('betr', API_URL, {
      method: 'POST',
      headers: headers,
      body,
      timeout: 15000,
      retries: 2
    });

    const responseData = await response.json();
    return responseData;
  } catch (error) {
    console.error('[BETR] Error fetching events by IDs:', error);
    return null;
  }
}

async function extractProjections(responseData) {
  const projections = [];
  const events = responseData?.data?.getEventsByIdsV2 ?? [];

  for (const event of events) {
    if (event.status !== 'SCHEDULED') continue;

    // Map 'CBB' to 'NCAAB'
    const league = event.league === 'CBB' ? 'NCAAB' : event.league;

    for (const team of event.teams ?? []) {
      for (const player of team.players ?? []) {
        for (const projection of player.projections ?? []) {
          if (
            projection.marketStatus === 'OPENED' &&
            projection.isLive === false &&
            projection.type === 'REGULAR' &&
            projection.allowedOptions?.length === 2 &&
            projection.allowedOptions.some(option => option.outcome === 'MORE') &&
            projection.allowedOptions.some(option => option.outcome === 'LESS')
          ) {
            projections.push({
              proj_id: projection.marketId,
              league: league,
              player_name: `${player.firstName} ${player.lastName}`,
              stat_type: projection.key,
              line: projection.value,
              over_odds_american: '-122',
              under_odds_american: '-122',
              over_odds_decimal: 1.82,
              under_odds_decimal: 1.82,
              matchup: event.name,
              start_time: new Date(event.date),
              source: 'Betr',
            });
          }
        }
      }
    }
  }

  //console.log(`[BETR] Extracted ${projections.length} projections`);
  return projections;
}

async function fetchBetr() {
  try {
    //console.log('[BETR] Starting fetchBetr');
    const eventIds = await getAllUpcomingEvents();

    if (eventIds.length === 0) {
      //console.log('[BETR] No events found, returning empty array');
      return [];
    }

    const responseData = await getEventsByIds(eventIds);
    //console.log('[BETR] Got response data from getEventsByIds');

    if (!responseData) {
      //console.log('[BETR] No response data, returning empty array');
      return [];
    }

    const projections = extractProjections(responseData);
    return projections;
  } catch (error) {
    console.error('[BETR] Error in fetchBetr:', error);
    return [];
  }
}
module.exports = {
  fetchBetr,
};