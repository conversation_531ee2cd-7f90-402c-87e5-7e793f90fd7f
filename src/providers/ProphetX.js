const { Projection } = require('../utils/types'); // Assuming types.ts is in ../utils
const { formatDecimal } = require('../utils/FormatUtility'); // Remove .ts extension
// const { decimalToAmerican } = require('../utils/ProjectionUtility'); // Not used directly here anymore
const { RequestManager } = require('../../dist/services/RequestManager');

// Hardcoded API token
const PROPHETX_API_TOKEN = "1b52db2c32feb10f30cb8bc6696de296";
// Base URL and Endpoints
const API_BASE_URL = 'https://cash.api.prophetx.co/partner';
const TOURNAMENTS_ENDPOINT = '/affiliate/get_tournaments'; // <<< New Endpoint
const EVENTS_ENDPOINT = '/affiliate/get_sport_events';
const MARKETS_ENDPOINT = '/v2/affiliate/get_markets';

// Get RequestManager instance
const requestManager = RequestManager.getInstance();

// Keywords to identify player props in display names
const PLAYER_PROP_IDENTIFIERS = [
    "Total Points", "Total Rebounds", "Total Assists", "Total Threes", "Three Pointers Made",
    "Total Steals", "Total Blocks", "Total Turnovers", "Total PRA", "Total PR", "Total PA",
    "Total Points, Rebounds & Assists", "Total Points & Rebounds", "Total Points & Assists",
    "Total Steals & Blocks", "Record a Double Double", "Record a Triple Double",
    // Add more as needed for other sports
];

// Helper function to safely get and calculate limit from cents to dollars
function getLimitInDollars(selection) {
    if (selection && typeof selection.value === 'number' && isFinite(selection.value)) {
        // Corrected: Round the value directly as it's already in dollars
        return Math.round(selection.value);
    }
    return undefined; // Return undefined if value is missing or invalid
}

/**
 * Makes a request to the ProphetX API.
 * @param {string} endpoint - The API endpoint path.
 * @param {URLSearchParams} [params] - Optional URL parameters.
 * @returns {Promise<any>} The JSON response data or null on failure.
 */
async function makeRequest(endpoint, params) {
    const url = new URL(API_BASE_URL + endpoint);
    if (params) {
        url.search = params.toString();
    }

    try {
        const response = await requestManager.fetch('prophetx', url.toString(), {
            method: 'GET',
            headers: {
                'Authorization': PROPHETX_API_TOKEN,
                'Accept': 'application/json'
            },
            timeout: 15000,
            retries: 2
        });

        if (!response.ok) {
            const errorBody = await response.text().catch(() => 'Could not read error body');
            if (response.status === 401) {
                 console.error(`[ProphetX] API request failed for ${endpoint} with status 401 (Unauthorized). Check API Token.`);
            } else {
                console.error(`[ProphetX] API request failed for ${endpoint} with status ${response.status}. Body: ${errorBody.substring(0, 200)}`);
            }
            return null;
        }
        return await response.json();
    } catch (error) {
        console.error(`[ProphetX] Error during request to ${endpoint}:`, error);
        return null;
    }
}

/**
 * Fetches player prop projections from the ProphetX API for all active tournaments.
 * @returns {Promise<Projection[]>} An array of projections.
 */
async function fetchProphetX() {
    if (!PROPHETX_API_TOKEN) {
        console.error('[ProphetX] API token is missing.');
        return [];
    }

    let allProjections = [];
    const uniqueStatTypes = new Set();
    const activeTournaments = []; // Array to hold { id, name, sportName }

    // --- Step 1: Fetch All Active Tournaments ---
    console.log("[ProphetX] Fetching list of active tournaments...");
    const tournamentParams = new URLSearchParams({ has_active_events: 'true' });
    const tournamentsResponse = await makeRequest(TOURNAMENTS_ENDPOINT, tournamentParams);

    if (tournamentsResponse?.data?.tournaments) {
        tournamentsResponse.data.tournaments.forEach(t => {
            if (t.id && t.name && t.sport?.name) {
                activeTournaments.push({
                    id: t.id,
                    name: t.name, // This will be our "league" name
                    sportName: t.sport.name
                });
            } else {
                 console.warn(`[ProphetX] Skipping tournament due to missing id, name, or sport name:`, t);
            }
        });
        console.log(`[ProphetX] Found ${activeTournaments.length} active tournaments.`);
    } else {
        console.error('[ProphetX] Failed to fetch or parse active tournaments. Response:', tournamentsResponse);
        return []; // Cannot proceed without tournaments
    }

    if (activeTournaments.length === 0) {
         console.warn('[ProphetX] No active tournaments found with events. Exiting.');
         return [];
    }
    // --- End Step 1 ---


    // --- Step 2 & 3: Fetch Events for Each Active Tournament ---
    const eventFetchPromises = [];
    console.log("[ProphetX] Preparing to fetch events for all active tournaments...");
    for (const tournament of activeTournaments) {
        const params = new URLSearchParams({ tournament_id: tournament.id.toString() });
        // Store tournament info with the promise
        eventFetchPromises.push(
            makeRequest(EVENTS_ENDPOINT, params).then(data => ({
                tournamentId: tournament.id,
                league: tournament.name, // Use tournament name as league
                sport: tournament.sportName,
                data: data // The response from makeRequest
            }))
        );
    }

    console.log(`[ProphetX] Fetching events for ${eventFetchPromises.length} tournaments...`);
    const eventResults = await Promise.all(eventFetchPromises);
    // --- End Step 2 & 3 ---


    // --- Step 4: Process Event Results ---
    const eventIdsToFetchMarkets = {}; // { eventId: { league, sport, eventName, startTime } }
    let totalEventsFound = 0;

    for (const result of eventResults) {
        if (result.data?.data?.sport_events) {
            const events = result.data.data.sport_events;
            totalEventsFound += events.length;
            // console.log(`[ProphetX DBG] Received ${events.length} events for ${result.league} (ID: ${result.tournamentId})`); // Optional details
            events.forEach(event => {
                if (event.event_id && event.status === 'not_started' && event.name && event.scheduled) {
                     const startTime = new Date(event.scheduled);
                     const hoursUntilStart = (startTime.getTime() - Date.now()) / (1000 * 60 * 60);
                     // Keep the time filter to avoid excessively old/future events
                     if (hoursUntilStart >= -2 && hoursUntilStart < 72) {
                         eventIdsToFetchMarkets[event.event_id] = {
                            league: result.league, // Use tournament name from the result context
                            sport: result.sport, // Use sport name from the result context
                            eventName: event.name,
                            startTime: startTime
                         };
                    }
                }
            });
        } else {
             // Only warn if the request didn't fail but data structure was wrong
             if (result.data !== null) {
                  console.warn(`[ProphetX] No events data found or unexpected structure for tournament: ${result.league} (ID: ${result.tournamentId})`);
             }
        }
    }
    console.log(`[ProphetX] Processed ${totalEventsFound} total events across all tournaments.`);
    // --- End Step 4 ---


    // --- Step 5: Fetch and Process Markets (Existing Logic) ---
    const marketFetchPromises = [];
    const eventIds = Object.keys(eventIdsToFetchMarkets);
    console.log(`[ProphetX] Fetching markets for ${eventIds.length} relevant events...`);

    for (const eventId of eventIds) {
        const params = new URLSearchParams({
            event_id: eventId,
            get_all_market: 'true',
        });
        marketFetchPromises.push(makeRequest(MARKETS_ENDPOINT, params).then(data => ({ eventId, data })));
    }

    const marketResults = await Promise.all(marketFetchPromises);

    // Process Market Data
    for (const result of marketResults) {
         // console.log(`[ProphetX DBG] Processing event ID: ${result.eventId}`); // Keep commented
         if (result.data?.data?.markets) {
             const eventInfo = eventIdsToFetchMarkets[result.eventId];
             if (eventInfo) {
                  // console.log(`[ProphetX DBG] Found ${result.data.data.markets.length} markets for event ${result.eventId}`); // Keep commented
                 const markets = result.data.data.markets;
                 // Pass sport info if processMarkets needs it eventually
                 const eventProjections = processMarkets(markets, eventInfo.league, eventInfo.sport, eventInfo.eventName, eventInfo.startTime, uniqueStatTypes);
                 allProjections = allProjections.concat(eventProjections);
                 // console.log(`[ProphetX DBG] Processed event ${result.eventId}, added ${eventProjections.length} projections. Total now: ${allProjections.length}`); // Keep commented - too verbose
             } else {
                  // console.warn(`[ProphetX DBG] Event info not found in map for event ID: ${result.eventId}`); // Keep commented
             }
         } // ... (keep existing error/null handling for market response) ...
         else if (result.data?.data?.event_id && !result.data.data.markets) { /* No markets */ }
         else if (result.data === null) { /* Request failed */ }
         else { /* Unexpected structure */ }
    }
    // --- End Step 5 ---


    // --- Log Unique Stat Types Found ---
    console.log("--- [ProphetX] Unique Stat Types Found ---");
    if (uniqueStatTypes.size === 0) {
        console.log(" (None passed final validation or no markets found)");
    } else {
        // Sort for consistent output
        const sortedStats = Array.from(uniqueStatTypes).sort();
        sortedStats.forEach(stat => console.log(` -> ${stat}`));
    }
    console.log("-----------------------------------------");
    // --- End Logging Stat Types ---

    console.log(`[ProphetX] Total player prop projections processed: ${allProjections.length}`); // Final count
    return allProjections;
}

// Helper function to format American odds correctly
function formatAmericanOdds(odds) {
    if (odds === null || odds === undefined) {
        return null;
    }
    const num = Number(odds); // Ensure it's a number
    if (isNaN(num)) {
        return null; // Return null if conversion fails
    }
    if (num > 0) {
        return `+${num}`; // Prepend '+' for positive odds
    }
    return String(num); // Convert negative or zero odds to string
}

/**
 * Processes the markets array from the API response for a single event.
 * @param {Array} markets - The array of market objects from ProphetX API.
 * @param {string} league - The league name (Tournament Name from API).
 * @param {string} sport - The sport name.
 * @param {string} eventName - The name of the event (matchup).
 * @param {Date} startTime - The start time of the event.
 * @param {Set<string>} uniqueStatTypes - Set to collect unique stat types.
 * @returns {Projection[]} An array of formatted projections.
 */
// Add 'sport' parameter
function processMarkets(markets, league, sport, eventName, startTime, uniqueStatTypes) {
    const projections = [];

    for (const [index, market] of markets.entries()) {
        // --- Player Name Extraction - Revised ---
        let marketPlayerName = market.player_name;
        // Fallback if player_name is missing
        if (!marketPlayerName && market.player_id && market.display_name) {
            const categoryName = market.category_name;
            let potentialName = null;

            // Try splitting by category name if it exists and is part of the display name
            if (categoryName && market.display_name.includes(categoryName)) {
                potentialName = market.display_name.split(categoryName)[0].trim();
            }
            // Original fallback using identifiers list (might be less needed now)
            else {
                 for (const identifier of PLAYER_PROP_IDENTIFIERS) {
                     if (market.display_name.includes(identifier)) {
                         potentialName = market.display_name.split(identifier)[0].trim();
                         break;
                     }
                 }
                  // Original fallback for " Total "
                  if (!potentialName && market.display_name.includes(" Total ")) {
                      potentialName = market.display_name.split(" Total ")[0].trim();
                  }
            }


            // Basic validation for the extracted name
            if (potentialName && (potentialName.includes(' ') || /^[a-zA-Z'. -]+$/.test(potentialName))) {
                marketPlayerName = potentialName;
            }
        }
        // --- END Player Name Extraction ---

        if (!marketPlayerName || !market.selections || market.selections.length < 2) continue;

        // --- ADDED: Clean Player Name ---
        let cleanedPlayerName = marketPlayerName.trim(); // Start with the determined name
        if (cleanedPlayerName.toLowerCase().endsWith(" total")) {
             cleanedPlayerName = cleanedPlayerName.substring(0, cleanedPlayerName.length - 5).trim();
             // console.log(`[ProphetX DBG] Cleaned player name: '${marketPlayerName}' -> '${cleanedPlayerName}'`); // Optional debug log
        }
        // --- END Added Clean ---

        // --- Yes/No Props Handling ---
        const isDoubleDouble = market.display_name?.toLowerCase().includes('double double');
        const isTripleDouble = market.display_name?.toLowerCase().includes('triple double');
        let handledAsYesNo = false;
        if (isDoubleDouble || isTripleDouble) {
            const yesSelection = market.selections.find(s => s.display_name?.toLowerCase() === 'yes');
            const noSelection = market.selections.find(s => s.display_name?.toLowerCase() === 'no');
            if (yesSelection && noSelection) {
                 handledAsYesNo = true;
                 const line = 0.5;
                 const overOddsAmerican = yesSelection.odds;
                 const underOddsAmerican = noSelection.odds;
                 const stat_type = isDoubleDouble ? "Double Double" : "Triple Double";
                 const convertAmericanToDecimal = (american) => { /* ... */ if(american === null || american === undefined) return undefined; if(typeof american !== 'number' && typeof american !== 'string') return undefined; if(american === 'EVEN') return 2.0; const num = typeof american === 'string' ? parseInt(american.replace('+','')) : american; if(isNaN(num)) return undefined; if(num === 100) return 2.0; if(num > 0) return (num / 100) + 1; if(num === -100) return 2.0; return (100 / Math.abs(num)) + 1; };
                 const overOddsDecimal = convertAmericanToDecimal(overOddsAmerican);
                 const underOddsDecimal = convertAmericanToDecimal(underOddsAmerican);
                 if (overOddsDecimal === undefined && underOddsDecimal === undefined) continue;
                 const yesOutcomeId = yesSelection.outcome_id || yesSelection.id || yesSelection.line_id || 'yes';
                 const noOutcomeId = noSelection.outcome_id || noSelection.id || noSelection.line_id || 'no';
                 const marketIdPart = market.id || market.name?.replace(/\s+/g, '_') || stat_type.replace(/\s+/g, '_');
                 const proj_id = `prophetx_${marketIdPart}_${yesOutcomeId}_${noOutcomeId}`;

                 // Extract ProphetX limits for Yes/No (Treating Yes as Over, No as Under)
                 const prophetx_over_limit = getLimitInDollars(yesSelection);
                 const prophetx_under_limit = getLimitInDollars(noSelection);

                 const projection = {
                     proj_id, league, player_name: cleanedPlayerName, stat_type, line: formatDecimal(line),
                     over_odds_american: formatAmericanOdds(overOddsAmerican),
                     under_odds_american: formatAmericanOdds(underOddsAmerican),
                     over_odds_decimal: overOddsDecimal !== undefined ? formatDecimal(overOddsDecimal, 3) : undefined,
                     under_odds_decimal: underOddsDecimal !== undefined ? formatDecimal(underOddsDecimal, 3) : undefined,
                     matchup: eventName, start_time: startTime, source: 'ProphetX', sport, // <<< Add sport if needed later
                     prophetx_over_limit, // Added limit
                     prophetx_under_limit // Added limit
                 };
                 if (projection.player_name && projection.stat_type && projection.line !== null && !isNaN(projection.line)) {
                     uniqueStatTypes.add(projection.stat_type);
                     projections.push(projection);
                 } else { /* Warn skip */ }
            } else { /* Warn no Yes/No found */ }
        }
        // --- END Yes/No Handling ---

        // --- Standard O/U Handling ---
        if (!handledAsYesNo) {
             const overSelection = market.selections.find(s => s.display_name?.toLowerCase().startsWith('over '));
             const underSelection = market.selections.find(s => s.display_name?.toLowerCase().startsWith('under '));
             if (!overSelection || !underSelection || typeof overSelection.line !== 'number' || overSelection.line !== underSelection.line ) {
                  continue;
             }
             const line = overSelection.line;
             const overOddsAmerican = overSelection.odds;
             const underOddsAmerican = underSelection.odds;

             // Convert odds and check for nulls
             const convertAmericanToDecimal = (american) => { /* ... */ if(american === null || american === undefined) return undefined; if(typeof american !== 'number' && typeof american !== 'string') return undefined; if(american === 'EVEN') return 2.0; const num = typeof american === 'string' ? parseInt(american.replace('+','')) : american; if(isNaN(num)) return undefined; if(num === 100) return 2.0; if(num > 0) return (num / 100) + 1; if(num === -100) return 2.0; return (100 / Math.abs(num)) + 1; };
             const overOddsDecimal = convertAmericanToDecimal(overOddsAmerican);
             const underOddsDecimal = convertAmericanToDecimal(underOddsAmerican);
             if (overOddsDecimal === undefined && underOddsDecimal === undefined) {
                  // This is where the 'Singles' example gets skipped correctly
                  continue;
             }

             // --- Stat Type Extraction - Revised ---
             let stat_type = "Unknown";
             const categoryName = market.category_name;
             const marketSubType = market.sub_type;
             const marketName = market.name; // Example: "Fixed total 19.5" - might need cleanup
             const displayNameLower = market.display_name?.toLowerCase() || "";

             // 1. Prioritize category_name if it's specific and not just "Props"
             if (categoryName && categoryName.toLowerCase() !== 'props' && categoryName.trim() !== '') {
                 stat_type = categoryName.trim();
             // 2. Check if market.sub_type seems useful (e.g., starts with 'player_total_')
             } else if (marketSubType?.startsWith("player_total_")) {
                 // Clean up the sub_type (remove prefix, replace underscores, title case)
                 stat_type = marketSubType.replace("player_total_", "")
                                       .replace(/_/g, " ")
                                       .split(' ')
                                       .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                                       .join(' ');
                 // Specific mapping for consistency if needed
                 if (stat_type === 'Points Rebounds Assists') stat_type = 'Pts + Reb + Ast';
                 // Add other similar mappings here if sub_type gives variations
                 // e.g., if (stat_type === 'Triple Double') stat_type = 'Triple+Double';
             // 3. Fallback: Try to extract from display_name (less reliable, keep simple)
             //    This part is tricky. Let's use a simple heuristic for common patterns
             //    If display name has "Total", split by that. This is a guess.
             } else if (displayNameLower.includes(" total ")) {
                 // Attempt to get the part after the player name and before " Total"
                 // This relies on marketPlayerName being correctly identified first.
                 const potentialStat = market.display_name?.substring(cleanedPlayerName.length).split(" Total ")[0].trim();
                 if (potentialStat && potentialStat.length > 1) { // Basic check
                     stat_type = potentialStat;
                     // Add specific mappings here if needed
                      if (stat_type === "Points, Rebounds & Assists") stat_type = "Pts + Reb + Ast";
                      if (stat_type === "Points & Rebounds") stat_type = "Pts + Reb";
                      if (stat_type === "Points & Assists") stat_type = "Pts + Ast";
                      if (stat_type === "Steals & Blocks") stat_type = "Stl + Blk";
                      if (stat_type === "Three Pointers Made") stat_type = "Threes";
                 }
             } else if (categoryName && categoryName.trim() !== '') {
                  // Added: If category name WAS 'props' but exists, use it as fallback
                  stat_type = categoryName.trim();
             }
              // Add specific mappings if stat_type needs standardization
              if (stat_type === "Three Pointers Made") stat_type = "Threes";
              if (stat_type === "Points, Rebounds & Assists") stat_type = "Pts + Reb + Ast";
              if (stat_type === "Points & Rebounds") stat_type = "Pts + Reb";
              if (stat_type === "Points & Assists") stat_type = "Pts + Ast";
              if (stat_type === "Steals & Blocks") stat_type = "Stl + Blk";
              // --- ADDED: Standardize Home Runs variations ---
              if (stat_type.toLowerCase().includes("home run")) { // More robust check
                  stat_type = "Home Runs";
              }
              // ... any other specific cleanups/mappings

             const overOutcomeId = overSelection.outcome_id || overSelection.id || overSelection.line_id || `over_${line}`;
             const underOutcomeId = underSelection.outcome_id || underSelection.id || underSelection.line_id || `under_${line}`;
             const marketIdPart = market.id || market.name?.replace(/\s+/g, '_') || stat_type.replace(/\s+/g, '_') || `market_${index}`; // Ensure part exists

             const proj_id = `prophetx_${marketIdPart}_${overOutcomeId}_${underOutcomeId}`;

              // Extract ProphetX limits for Over/Under
             const prophetx_over_limit = getLimitInDollars(overSelection);
             const prophetx_under_limit = getLimitInDollars(underSelection);

             const projection = {
                 proj_id, league, player_name: cleanedPlayerName, stat_type: stat_type.trim(), line: formatDecimal(line),
                 over_odds_american: formatAmericanOdds(overOddsAmerican),
                 under_odds_american: formatAmericanOdds(underOddsAmerican),
                 over_odds_decimal: overOddsDecimal !== undefined ? formatDecimal(overOddsDecimal, 3) : undefined,
                 under_odds_decimal: underOddsDecimal !== undefined ? formatDecimal(underOddsDecimal, 3) : undefined,
                 matchup: eventName, start_time: startTime, source: 'ProphetX', sport,
                 prophetx_over_limit, // Added limit
                 prophetx_under_limit // Added limit
             };

             // Final validation check remains
             if (projection.player_name && projection.stat_type !== "Unknown" && projection.line !== null && !isNaN(projection.line)) {
                 uniqueStatTypes.add(projection.stat_type);
                 projections.push(projection);
             } else {
                   // --- ADDED: More detailed skip logging ---
                   let skipReason = [];
                   if (!projection.player_name) skipReason.push("Missing player_name");
                   if (projection.stat_type === "Unknown") skipReason.push("Stat type is Unknown");
                   if (projection.line === null || isNaN(projection.line)) skipReason.push(`Invalid line (${projection.line})`);
                   if (skipReason.length > 0) {
                       // console.log(`[ProphetX DBG] Skipping projection for ${market.display_name || 'Unknown Market'} (League: ${league}, Sport: ${sport}). Reason(s): ${skipReason.join(', ')}. Extracted stat_type: ${stat_type}`);
                   }
                   // --- END Added Logging ---
                   /* Original Logs below - keep commented unless needed
                   if(projection.stat_type === "Unknown") {
                        // console.log(`[ProphetX DBG] Skipping market due to Unknown stat type: ${market.display_name} (Cat: ${categoryName}, SubType: ${marketSubType})`);
                   }
                   if(!projection.player_name) {
                       // console.log(`[ProphetX DBG] Skipping market due to Missing player name: ${market.display_name}`);
                   }
                   */
             }
        } // End standard O/U handling
    } // End market loop
    return projections;
}

module.exports = {
    fetchProphetX
};