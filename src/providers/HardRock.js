const WebSocket = require('ws');
const HttpsProxyAgent = require('https-proxy-agent'); // Keep for WebSocket
const fs = require('fs').promises;
const path = require('path');
const { spawn } = require('child_process');

let proxiesCache = null;
let rootLadderCache = null;

class HardRockScraper {
  constructor() {
    this.url = "https://api.hardrocksportsbook.com/java-graphql/graphql";
    this.headers = {
      "Accept": "application/json",
      "Accept-Language": "en-US,en;q=0.9",
      "Content-Type": "application/json",
      "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
      "Origin": "https://app.hardrock.bet",
      "Priority": "u=1, i",
      "Referer": "https://app.hardrock.bet/",
      "Sec-CH-UA": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
      "Sec-CH-UA-Mobile": "?1",
      "Sec-CH-UA-Platform": "\"Android\"",
      "Sec-Fetch-Dest": "empty",
      "Sec-Fetch-Mode": "cors",
      "Sec-Fetch-Site": "cross-site"
    };
    this.sportsMap = {
      "NFL": { competitionId: "691198679103111169", sport: "american football", league: "NFL" },
      "CFB": { competitionId: "700696001136984066", sport: "american football", league: "CFB" },
      "MLB": { competitionId: "691030499319709699", sport: "baseball", league: "MLB" },
      "NBA": { competitionId: "691033199537586178", sport: "basketball", league: "NBA" },
      "NHL": { competitionId: "691036012789334019", sport: "hockey", league: "NHL" },
    };
    this.eventMetadata = {};
    this.totalEventIds = 0;
    this.processedEventIds = 0;
    this.proxies = [];
    this.projections = [];
    this.rootLadder = [];
    this.maxProxyRetries = 3;

    // Automatically initialize when the class is instantiated
    this.initPromise = this.initialize();
  }

  async initialize() {
    await this.loadProxies();
    await this.loadRootLadder();
  }

  async loadProxies() {
    if (!proxiesCache) {
      const proxyFile = path.join(__dirname, 'proxies', 'proxies.txt');
      const proxyData = await fs.readFile(proxyFile, 'utf8');
      proxiesCache = proxyData.split('\n').filter(line => line.trim() !== '');
    }
    this.proxies = proxiesCache;
  }

  async loadRootLadder() {
    if (!rootLadderCache) {
      const rootLadderFile = path.join(__dirname, '..', 'utils', 'rootLadder.json');
      const rootLadderData = await fs.readFile(rootLadderFile, 'utf8');
      rootLadderCache = JSON.parse(rootLadderData).PriceAdjustmentDetailsResponse.rootLadder;
    }
    this.rootLadder = rootLadderCache;
  }

  getRandomProxy() {
    return this.proxies[Math.floor(Math.random() * this.proxies.length)];
  }

  async getCompetitionEvents(competitionId, proxy) {
    const payload = {
      operationName: "betSync",
      query: `
          query betSync(
            $filters: [Filter]
            $segment: String
            $region: String
            $language: String
            $channel: String
          ) {
            betSync(cmsSegment: $segment, region: $region, language: $language, channel: $channel) {
              events(filters: $filters) {
                data {
                  id
                  name
                  eventTime
                  sport
                  compName
                }
                count
              }
            }
          }
        `,
      variables: {
        channel: "FLORIDA_ONLINE",
        segment: "fl",
        region: "us",
        language: "enus",
        filters: [
          { field: "compId", values: [competitionId] },
          { field: "outright", value: "false" }
        ]
      }
    };
    const url = this.url;
    const headers = this.headers;

    // The proxy parameter is now passed to HardRockTLS.py.
    // HardRockTLS.py has been updated to accept and use this proxy information.

    let retries = 0;
    while (retries < this.maxProxyRetries) {
      try {
        const currentProxy = proxy || "None"; // Pass "None" if proxy is not set
        console.log(`[HARDROCK] Attempting request for competition ID ${competitionId} using proxy: ${currentProxy === "None" ? "None" : "********"}`);
        const pythonArgs = [
          path.join(__dirname, 'HardRockTLS.py'),
          url,
          JSON.stringify(headers),
          JSON.stringify(payload),
          currentProxy // Pass the proxy string to the Python script
        ];
        
        const pythonProcess = spawn('python3', pythonArgs);

        let result = '';
        let errorOutput = '';

        pythonProcess.stdout.on('data', (data) => {
          result += data.toString();
        });

        pythonProcess.stderr.on('data', (data) => {
          errorOutput += data.toString();
        });

        const exitCode = await new Promise((resolve) => {
          pythonProcess.on('close', resolve);
        });

        if (errorOutput) {
          console.error(`[HARDROCK] Python script stderr: ${errorOutput}`);
        }

        if (exitCode !== 0) {
          console.error(`[HARDROCK] Python script exited with code ${exitCode}. Retrying... Attempt ${retries + 1}/${this.maxProxyRetries}`);
          retries++;
          if (retries >= this.maxProxyRetries) {
            console.error("[HARDROCK] Max proxy retries exceeded after Python script failure.");
            return null;
          }
          continue; // Retry with a new Python process call
        }

        try {
          const parsedResult = JSON.parse(result);
          if (parsedResult.error) {
            console.error(`[HARDROCK] Python script returned error: ${parsedResult.error}. Retrying... Attempt ${retries + 1}/${this.maxProxyRetries}`);
            retries++;
            if (retries >= this.maxProxyRetries) {
              console.error("[HARDROCK] Max proxy retries exceeded after Python script error.");
              return null;
            }
            continue; // Retry
          } else if (parsedResult.status === 200) {
            console.log(`[HARDROCK] Python script response status: ${parsedResult.status}`);
            return parsedResult.data;
          } else {
            console.error(`[HARDROCK] Python script returned non-200 status: ${parsedResult.status}. Retrying... Attempt ${retries + 1}/${this.maxProxyRetries}`);
            retries++;
            if (retries >= this.maxProxyRetries) {
              console.error("[HARDROCK] Max proxy retries exceeded after non-200 status from Python script.");
              return null;
            }
            continue; // Retry
          }
        } catch (e) {
          console.error(`[HARDROCK] Error parsing Python script result: ${e}. Result: "${result}". Retrying... Attempt ${retries + 1}/${this.maxProxyRetries}`);
          retries++;
          if (retries >= this.maxProxyRetries) {
            console.error("[HARDROCK] Max proxy retries exceeded after parsing error.");
            return null;
          }
          continue; // Retry
        }
      } catch (error) {
        console.error(`[HARDROCK] An unexpected error occurred in getCompetitionEvents:`, error);
        retries++;
        if (retries >= this.maxProxyRetries) {
          console.error("[HARDROCK] Max proxy retries exceeded after unexpected error.");
          return null;
        }
        console.info(`[HARDROCK] Retrying... Attempt ${retries + 1}/${this.maxProxyRetries}`);
        // No need to change proxy here as the Python script doesn't use it directly yet
      }
    }
    console.error("[HARDROCK] All retries failed for getCompetitionEvents.");
    return null; // Return null if all retries failed
  }

  cleanTeamName(teamName) {
    return teamName.replace(/#\d+\s*/, '').trim();
  }

  parseGame(game) {
    let homeTeam, awayTeam;
    if (game.includes(" vs. ")) {
      [homeTeam, awayTeam] = game.split(" vs. ");
    } else if (game.includes(" vs ")) {
      [homeTeam, awayTeam] = game.split(" vs ");
    } else {
      throw new Error(`Game format is invalid: ${game}`);
    }

    homeTeam = this.cleanTeamName(homeTeam);
    awayTeam = this.cleanTeamName(awayTeam);

    return { homeTeam, awayTeam, formattedGame: `${awayTeam} @ ${homeTeam}` };
  }

  extractEventData(eventData, sport, league) {
    const extractedEvents = [];
    const events = eventData?.data?.betSync?.events?.data || [];

    for (const event of events) {
      try {
        const { homeTeam, awayTeam, formattedGame } = this.parseGame(event.name);
        const timestamp = new Date(event.eventTime);

        this.eventMetadata[event.id] = {
          sport,
          league,
          game: formattedGame,
          homeTeam,
          awayTeam,
          timestamp: timestamp
        };
        extractedEvents.push(event.id);
      } catch (error) {
        console.error(`Error parsing game: ${error.message}`);
      }
    }
    return extractedEvents;
  }

  async processPlayerPropsData(eventId, responseData) {
    if (!responseData?.displayed || !this.eventMetadata[eventId]) {
      return;
    }

    const markets = responseData?.markets || [];
    const matchup = responseData.names?.veryshortName;

    for (const market of markets) {
      if (market.name.includes("Record") || market.name.includes("To Score")) {
        continue;
      }

      if (market.displayed && market.state === "OPEN" && market.selections?.length === 2) {
        const overSelection = market.selections.find(s => s.type === "Over");
        const underSelection = market.selections.find(s => s.type === "Under");

        if (!overSelection || !underSelection) continue;

        const [player_name, stat_type] = market.name.split(" - ");
        if (!player_name || !stat_type) continue;

        let league = this.eventMetadata[eventId].league;
        if (league === "NBA" && stat_type.includes("1st Quarter")) {
          league = "NBA1Q";
        }

        const line = parseFloat(overSelection.name.split(" ")[1]);
        if (!line) continue;

        const overRootDetails = this.rootLadder.find(l => l.rootIndex === overSelection.rootIdx);
        const underRootDetails = this.rootLadder.find(l => l.rootIndex === underSelection.rootIdx);

        if (!overRootDetails || !underRootDetails) continue;

        let startDate = this.eventMetadata[eventId].timestamp;

        // Create deep links for bet slip
        const overDeepLink = `https://share.hardrock.bet/Pt0T/bet?deep_link_value=hardrock://betslip/${overSelection.id}`;
        const underDeepLink = `https://share.hardrock.bet/Pt0T/bet?deep_link_value=hardrock://betslip/${underSelection.id}`;

        this.projections.push({
          proj_id: `${eventId}-${market.id}`,
          league,
          player_name,
          stat_type,
          line,
          matchup,
          over_odds_decimal: overRootDetails.decimal,
          under_odds_decimal: underRootDetails.decimal,
          over_odds_american: overRootDetails.moneyline,
          under_odds_american: underRootDetails.moneyline,
          odds_over_american: overRootDetails.moneyline,
          odds_under_american: underRootDetails.moneyline,
          start_time: startDate,
          source: 'HardRock',
          over_deep_link: overDeepLink,
          under_deep_link: underDeepLink
        });
      }
    }
  }

  async logPlayerProps() {
    if (this.projections.length > 0) {
      const timestamp = DateTime.now().toFormat('yyyyMMdd_HHmmss');
      const filename = `logs/player_props_${timestamp}.json`;

      try {
        await fs.mkdir('logs', { recursive: true });
        await fs.writeFile(
          filename,
          JSON.stringify(this.projections, null, 2),
          'utf8'
        );
        console.log(`Player props logged to ${filename}`);
      } catch (error) {
        console.error(`Failed to log player props: ${error.message}`);
      }
    }
  }

  async processMarket(eventId, market) {
    return this.processPlayerPropsData(eventId, {
      displayed: true,
      eventTime: this.eventMetadata[eventId]?.timestamp,
      names: { veryshortName: this.eventMetadata[eventId]?.game },
      markets: [market]
    });
  }

  async getEventDetails(eventIds, proxy) {
    const uri = 'wss://api.hardrocksportsbook.com/graphql-ws'; // Reverted to original WebSocket URI
    const headers = {
      "Host": "api.hardrocksportsbook.com",
      "Connection": "Upgrade",
      "User-Agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", // Updated User-Agent
      "Upgrade": "websocket",
      "Origin": "https://app.hardrock.bet", // Ensure correct Origin
      "Accept-Language": "en-US,en;q=0.9", // Updated Accept-Language
      "Pragma": "no-cache", // Kept as per instructions for WebSocket
      "Cache-Control": "no-cache", // Kept as per instructions for WebSocket
      "Sec-WebSocket-Version": "13",
      "Sec-WebSocket-Key": Buffer.from(Math.random().toString()).toString('base64'),
      "Sec-WebSocket-Extensions": "permessage-deflate; client_max_window_bits"
    };

    const [host, port, username, password] = proxy.split(':');
    const proxyUrl = `http://${username}:${password}@${host}:${port}`; // Ensure correct protocol
    const agent = new HttpsProxyAgent(proxyUrl); // Use HttpsProxyAgent

    return new Promise((resolve, reject) => {
      const ws = new WebSocket(uri, {
        headers: headers,
        agent: agent,
        protocol: 'graphql-transport-ws' // Added protocol based on overview
      });

      ws.on('open', async () => {
        const loginMessage = JSON.stringify({
          SportsbookLoginRequest: {
            application: "sportsbook",
            channel: "FLORIDA_ONLINE", // Changed from NEW_JERSEY_ONLINE
            locale: "enus-us-x-fl" // Changed from enus-us-x-nj
          }
        });

        ws.send(loginMessage);

        ws.once('message', async (data) => {
          await this.subscribeToAllEvents(ws, eventIds);
        });
      });

      ws.on('close', () => {
        resolve();
      });

      ws.on('error', (error) => {
        reject(error);
      });
    });
  }

  async subscribeToAllEvents(ws, eventIds) {
    const batchSize = 100;
    for (let i = 0; i < eventIds.length; i += batchSize) {
      const batch = eventIds.slice(i, i + batchSize);
      const subscriptionMessage = JSON.stringify({
        SubscriptionRequest: {
          subscribe: {
            ids: batch,
            mostBalancedMarkets: false
          }
        }
      });
      await ws.send(subscriptionMessage);
    }

    ws.on('message', async (data) => {
      const eventData = JSON.parse(data);
      if ("SubscriptionResponse" in eventData) {
        const responseData = eventData.SubscriptionResponse?.data;
        /*
        if (responseData) {
          try {
            const timestamp = DateTime.now().toFormat('yyyyMMdd_HHmmss');
            const filename = `logs/subscription_response_${responseData?.id || 'unknown'}_${timestamp}.json`;

            await fs.mkdir('logs', { recursive: true });
            await fs.writeFile(
              filename,
              JSON.stringify(responseData, null, 2),
              'utf8'
            );
          } catch (error) {
            console.error(`Failed to log response: ${error.message}`);
          }
        }
        */
        if (!responseData?.displayed) {
          this.processedEventIds++;
          if (this.processedEventIds >= this.totalEventIds) {
            ws.close();
          }
          return;
        }

        const eventId = responseData?.id;
        if (eventId && this.eventMetadata[eventId]) {
          const markets = responseData?.markets || [];
          for (const market of markets) {
            this.processMarket(eventId, market);
          }
        }

        this.processedEventIds++;
        if (this.processedEventIds >= this.totalEventIds) {
          ws.close();
        }
      }
    });
  }

  async fetchHardRock() {
    await this.initPromise;

    const allEventIds = [];
    const promises = [];
    const proxy = this.getRandomProxy();  // Fetch a single proxy for the batch

    for (const [sport, details] of Object.entries(this.sportsMap)) {
      promises.push(this.getCompetitionEvents(details.competitionId, proxy)
        .then((data) => {
          if (data) {
            const eventIds = this.extractEventData(data, details.sport, details.league);
            allEventIds.push(...eventIds);
          } else {
            console.log(`Failed to fetch or process data for ${details.league}`);
          }
        }));
    }

    await Promise.all(promises);
    this.totalEventIds = allEventIds.length;

    if (allEventIds.length > 0) {
      await this.getEventDetails(allEventIds, proxy);  // Reuse the proxy
      //await this.logPlayerProps();
    } else {
      console.log("No events to process.");
    }

    return this.projections;
  }
}

module.exports = HardRockScraper;

// Usage example:
// (async () => {
//   const scraper = new HardRockScraper();
//   await scraper.initialize();
//   const projections = await scraper.fetchHardRock();
//   console.log(JSON.stringify(projections, null, 2));
// })();
