import fs from 'fs';
import path from 'path';
import initCycleTLS from 'cycletls';
import { getProxy } from '../utils/proxy';
import { Projection } from '../utils/types';

// League endpoints map
const leagueEndpoints = {
    NBA: { url: 'https://sports.va.betmgm.com/en/sports/api/widget/widgetdata?layoutSize=Large&page=CompetitionLobby&sportId=7&regionId=9&competitionId=6004&compoundCompetitionId=1:6004&widgetId=/mobilesports-v1.0/layout/layout_us/modules/basketball/nba/nba-gamelines-complobbydesktop&shouldIncludePayload=true', league: 'NBA' },
    //NHL: { url: 'https://sports.va.betmgm.com/en/sports/api/widget/widgetdata?layoutSize=Large&page=SportLobby&sportId=12&widgetId=/mobilesports-v1.0/layout/layout_us/modules/hockey/hockeylobby&shouldIncludePayload=true', league: 'NHL' },
    MLB: { url: 'https://sports.va.betmgm.com/en/sports/api/widget/widgetdata?layoutSize=Large&page=SportLobby&sportId=23&widgetId=/mobilesports-v1.0/layout/layout_us/modules/baseball/baseballlobby&shouldIncludePayload=true', league: 'MLB' },
    LAX: { url: 'https://sports.va.betmgm.com/en/sports/api/widget/widgetdata?layoutSize=Small&page=SportLobby&sportId=88&widgetId=/mobilesports-v1.0/layout/layout_standards/modules/sportgrid&shouldIncludePayload=true', league: 'LAX' },
    NFL: { url: 'https://sports.va.betmgm.com/en/sports/api/widget/widgetdata?layoutSize=Large&page=CompetitionLobby&sportId=11&regionId=9&competitionId=35&compoundCompetitionId=1:35&widgetId=/mobilesports-v1.0/layout/layout_us/modules/football/nfl/nfl-defaultgridnfllobby&shouldIncludePayload=true', league: 'NFL' }
};

function cleanMatchupString(matchup) {
    // Using a regular expression to remove text in parentheses
    return matchup.replace(/\s*\([^)]*\)/g, '');
}

function formatOdds(odds) {
    // Check if odds are undefined or null before proceeding
    if (odds === undefined || odds === null) {
        //console.error('Odds value is undefined or null');
        return ''; // Return an empty string or some default value indicating missing data
    }

    // Prepend '+' if the odds are positive
    if (!odds) return "";

    return odds > 0 ? `+${odds}` : odds.toString();
}

function americanToDecimal(americanOdds) {
    if (americanOdds === 'Even') {
        americanOdds = '-100';
    }

    if (americanOdds.startsWith('+')) {
        return (parseInt(americanOdds.substring(1)) / 100) + 1;
    } else {
        return (100 / Math.abs(parseInt(americanOdds))) + 1;
    }
}

// Helper functions
function extractPlayerName(name) {
    const endIndex = name.indexOf(" (");
    return endIndex !== -1 ? name.substring(0, endIndex) : null;
}

function extractPlayerNameAlt(name) {
    const regex = /^(.*?)\s*\(/;
    const match = name.match(regex);
    return match ? match[1].trim() : null;
}

function extractStatType(name, league) {
    let statType = "";
    const startIndex = name.indexOf(": ") + 2; // Finds the start index of the stat type

    if (startIndex > 1) {
        statType = name.substring(startIndex);
    }

    // League-specific adjustments can be made here
    switch (league) {
        case 'MLB':
            // For MLB, stat type extraction already fits the required behavior
            break;
        case 'LAX':
            // For LAX, stat type extraction already fits the required behavior
            break;
        case 'NBA':
            // NBA-specific adjustments (if any) can be made here
            if (statType === 'Total points and asssists' || statType === 'Points and assists') {
                statType = 'Total points and assists';
            }
            break;
        case 'NFL':
            // NFL-specific adjustments (if any) can be made here
            if (statType === 'Pass completion') {
                statType = 'Pass completions';
            }
            if (statType === 'Total tackes and assists') {
                statType = 'Total tackles and assists';
            }
            break;
        // Additional cases for other leagues can be added as needed
        default:
            // Default behavior or adjustments for other leagues
            break;
    }

    return statType;
}


function getProjectionsFromFixture(fixtureResponse: any, league: string): Projection[] {
    const projections: Projection[] = [];
    
    if (!fixtureResponse?.fixture) {
        return projections;
    }

    // Get both markets and games
    const markets = fixtureResponse.fixture.markets || [];
    const games = fixtureResponse.fixture.games || [];
    const optionMarkets = fixtureResponse.fixture.optionMarkets || [];

    // Process all possible sources of projections
    const allMarkets = [...markets, ...games, ...optionMarkets];

    if (allMarkets.length === 0) {
        return projections;
    }

    // Try to find player props in any of these arrays
    for (const market of allMarkets) {
        // Try both results and options arrays
        const options = market.options || market.results || [];
        
        // Look for over/under options
        const overOption = options.find(opt => 
            (opt.name?.value?.startsWith('Over') || opt.totalsPrefix === 'Over')
        );
        const underOption = options.find(opt => 
            (opt.name?.value?.startsWith('Under') || opt.totalsPrefix === 'Under')
        );

        if (overOption && underOption) {
            const playerName = extractPlayerName(market.name?.value);
            const statType = extractStatType(market.name?.value, league);

            if (!playerName || !statType) continue;

            // Get the line from either the market or the option
            const line = parseFloat(market.attr || overOption.attr || overOption.name?.value?.split(' ')[1]);

            // Get odds from the appropriate location
            const overOddsAmerican = formatOdds(overOption.price?.americanOdds || overOption.americanOdds);
            const underOddsAmerican = formatOdds(underOption.price?.americanOdds || underOption.americanOdds);

            const over_odds_decimal = overOption.price?.odds || overOption.odds;
            const under_odds_decimal = underOption.price?.odds || underOption.odds;

            if (!line || !over_odds_decimal || !under_odds_decimal) continue;

            const projection = {
                proj_id: market.id.toString(),
                league,
                player_name: playerName,
                stat_type: statType.trim(),
                line,
                over_odds_american: overOddsAmerican,
                under_odds_american: underOddsAmerican,
                over_odds_decimal,
                under_odds_decimal,
                matchup: fixtureResponse.fixture.name.value,
                start_time: new Date(fixtureResponse.fixture.startDate),
                source: 'BetMGM'
            };

            projections.push(projection);
        }
    }

    return projections;
}

export async function fetchBetMGM() {
    const startTime = Date.now();
    const cycleTLS = await initCycleTLS();
    let allProjections: Projection[] = [];
    const proxy = getProxy();
    let totalFixtures = 0;
    let processedFixtures = 0;

    console.log('[BETMGM] Starting fetch...');

    try {
        // First, get all fixture IDs in parallel
        const leaguePromises = Object.entries(leagueEndpoints).map(async ([league, endpointInfo]) => {
            try {
                const response = await cycleTLS(endpointInfo.url, {
                    headers: {
                        authority: 'sports.va.betmgm.com',
                        accept: 'application/json, text/plain, */*',
                        'accept-language': 'en-US,en;q=0.9',
                        'x-app-context': 'default',
                        'x-from-product': 'sports',
                        'x-bwin-accessid': 'NjQ4MDQ1MWEtMmY1Ny00ODhkLTkxNTItNzA4MzY4MzM2YTE2',
                    },
                    ja3: '771,4865-4867-4866-49195-49199-52393-52392-49196-49200-49162-49161-49171-49172-51-57-47-53-10,0-23-65281-10-11-35-16-5-51-43-13-45-28-21,29-23-24-25-256-257,0',
                    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    proxy: proxy,
                    timeout: 15000
                }, 'get');

                const body = response.body as { [key: string]: any };
                let fixtureIds: string[] = [];

                if (league === 'NBA' || league === 'NFL') {
                    const composableWidget = body.widgets.find(w => w.type === "Composable");
                    if (composableWidget?.payload.items?.[0]?.activeChildren?.[0]?.payload.fixtures) {
                        fixtureIds = composableWidget.payload.items[0].activeChildren[0].payload.fixtures.map(fixture => fixture.id);
                    }
                } else {
                    fixtureIds = body.widgets[0]?.payload.fixtures?.map(fixture => fixture.id) || [];
                }

                return { league, fixtureIds };
            } catch (error) {
                console.error(`[BETMGM] Error fetching fixtures for ${league}:`, error);
                return { league, fixtureIds: [] as string[] };
            }
        });

        const leagueResults = await Promise.all(leaguePromises);
        totalFixtures = leagueResults.reduce((sum, { fixtureIds }) => sum + fixtureIds.length, 0);

        // Process fixtures with concurrency
        const maxConcurrency = 25;
        for (const { league, fixtureIds } of leagueResults) {
            if (fixtureIds.length === 0) continue;

            const chunks: string[][] = [];
            for (let i = 0; i < fixtureIds.length; i += maxConcurrency) {
                chunks.push(fixtureIds.slice(i, i + maxConcurrency));
            }

            for (const chunk of chunks) {
                const chunkPromises = chunk.map(async (fixtureId) => {
                    try {
                        const fixtureResponse = await cycleTLS(
                            `https://sports.va.betmgm.com/cds-api/bettingoffer/fixture-view?x-bwin-accessid=NjQ4MDQ1MWEtMmY1Ny00ODhkLTkxNTItNzA4MzY4MzM2YTE2&lang=en-us&country=US&userCountry=US&subdivision=US-Virginia&offerMapping=All&scoreboardMode=Full&fixtureIds=${fixtureId}&state=Latest&includePrecreatedBetBuilder=true&supportVirtual=false&isBettingInsightsEnabled=true&useRegionalisedConfiguration=true&includeRelatedFixtures=false&statisticsModes=All`,
                            {
                                headers: {
                                    authority: 'sports.va.betmgm.com',
                                    accept: 'application/json, text/plain, */*',
                                    'accept-language': 'en-US,en;q=0.9',
                                    'x-app-context': 'default',
                                    'x-from-product': 'sports',
                                    'x-bwin-accessid': 'NjQ4MDQ1MWEtMmY1Ny00ODhkLTkxNTItNzA4MzY4MzM2YTE2',
                                },
                                ja3: '771,4865-4867-4866-49195-49199-52393-52392-49196-49200-49162-49161-49171-49172-51-57-47-53-10,0-23-65281-10-11-35-16-5-51-43-13-45-28-21,29-23-24-25-256-257,0',
                                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                                proxy: proxy,
                                timeout: 15000
                            },
                            'get'
                        );
                        
                        return getProjectionsFromFixture(fixtureResponse.body, league);
                    } catch (error) {
                        console.error(`[BETMGM] Error processing fixture ${fixtureId}:`, error);
                        return [];
                    }
                });

                const chunkResults = await Promise.all(chunkPromises);
                allProjections.push(...chunkResults.flat());
                
                processedFixtures += chunk.length;
                const progress = ((processedFixtures / totalFixtures) * 100).toFixed(1);
                console.log(`[BETMGM] Progress: ${progress}% (${processedFixtures}/${totalFixtures})`);
            }
        }

        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        console.log(`[BETMGM] Fetch completed in ${duration.toFixed(1)}s`);
        console.log(`[BETMGM] Total projections: ${allProjections.length}`);

    } catch (error) {
        console.error('[BETMGM] Error in fetchBetMGM:', error);
    } finally {
        cycleTLS.exit();
    }

    return allProjections;
}
