const { RequestManager } = require('../services/RequestManager');

const URLS = {
    NBA: 'https://pick6.draftkings.com/?sport=NBA&_data=routes%2F_index',
    WNBA: 'https://pick6.draftkings.com/?sport=WNBA&_data=routes%2F_index',
    SOCCER: 'https://pick6.draftkings.com/?sport=SOCCER&_data=routes%2F_index',
    NFL: 'https://pick6.draftkings.com/?sport=NFL&_data=routes%2F_index',
    NHL: 'https://pick6.draftkings.com/?sport=NHL&_data=routes%2F_index',
    NCAAB: 'https://pick6.draftkings.com/?sport=CBB&_data=routes%2F_index',
    MMA: 'https://pick6.draftkings.com/?sport=UFC&_data=routes%2F_index',
    MLB: 'https://pick6.draftkings.com/?sport=MLB&_data=routes%2F_index'
};

const headers = {
    'accept': '*/*',
    'accept-language': 'en-US,en;q=0.9,la;q=0.8,en-GB;q=0.7',
    'priority': 'u=1, i',
    'sec-ch-ua': '"Not A(<PERSON>";v="8", "Chromium";v="132", "Google Chrome";v="132"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36'
};


function hasBothMarketSides(activeMarket) {
    // Check if we have both over and under selections
    const selections = activeMarket.activeSelections || [];
    const hasOver = selections.some(sel => sel.statLinePropositionId === 1);
    const hasUnder = selections.some(sel => sel.statLinePropositionId === 2);
    return hasOver && hasUnder;
}

function processResponse(data, league) {
    const projections = [];
    const pickableMap = data.pickableIdToPickableMap;
    const currentTime = new Date();

    // For MMA matchups
    const mmaMatchups = new Map();

    if (league === "MMA") {
        for (const [id, pickableData] of Object.entries(pickableMap)) {
            const competition = pickableData.pickable.pickableEntities[0]?.pickableCompetitions[0];
            if (!competition) continue;

            const competitionId = competition.competitionSummary.competitionId;
            const playerName = pickableData.pickable.pickableEntities[0].displayName.split(" ").pop();

            if (!mmaMatchups.has(competitionId)) {
                mmaMatchups.set(competitionId, { fighter1: playerName });
            } else {
                mmaMatchups.get(competitionId).fighter2 = playerName;
            }
        }
    }

    for (const [id, pickableData] of Object.entries(pickableMap)) {
        const { pickable, activeMarket } = pickableData;

        // Skip if basic validations fail
        if (pickable.isUnpickable) continue;
        if (!["Single", "AnyTime"].includes(pickable.pickType)) continue;
        if (!hasBothMarketSides(activeMarket)) continue;  // Skip if not both sides available

        const competition = pickable.pickableEntities[0]?.pickableCompetitions[0];
        if (!competition) continue;

        const startTime = new Date(competition.competitionSummary.startTime);
        if (startTime <= currentTime) continue;

        const timeStatus = competition.competitionSummary.timeStatus || "";
        const competitionState = competition.competitionSummary.competitionState || "";

        if (timeStatus !== "" && !["Pre-Game", "Pre-Fight"].includes(timeStatus) && competitionState !== "Upcoming") continue;

        const playerName = pickable.pickableEntities[0].displayName;
        let matchup = competition.competitionSummary.name;

        projections.push({
            proj_id: id.toString(),
            league,
            team: competition.team?.abbreviation || competition.team?.name || 'N/A',
            player_name: playerName,
            stat_type: pickable.marketCategory.marketName,
            line: parseFloat(activeMarket.targetValue),
            over_odds_american: "-135",
            under_odds_american: "-135",
            over_odds_decimal: 1.74,
            under_odds_decimal: 1.74,
            matchup,
            start_time: startTime,
            source: "Pick6"
        });
    }

    return projections;
}

async function fetchData(sport, url) {
    try {
        const requestManager = RequestManager.getInstance();
        const response = await requestManager.fetch('pick6', url, {
            method: 'GET',
            headers,
            timeout: 30000,
            retries: 2
        });

        const data = await response.json();
        return processResponse(data, sport);
    } catch (error) {
        console.error(`[PICK 6] Error fetching ${sport} data:`, error.message);
        return [];
    }
}

async function fetchPick6() {
    const requests = Object.entries(URLS).map(([sport, url]) =>
        fetchData(sport, url)
    );

    const results = await Promise.all(requests);
    return results.flat();
}

module.exports = {
    fetchPick6
};