import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';
import { logger } from '../utils/logger';

export interface ProxyState {
  id: string;
  url: string;
  successCount: number;
  failureCount: number;
  totalLatency: number;
  avgLatency: number;
  status: 'active' | 'cooling_down' | 'dead';
  lastUsedTimestamp: number;
  lastFailureTimestamp?: number;
  cooldownUntil?: number;
  consecutiveFailures: number;
}

interface ProxyConfig {
  cooldownPeriod?: number;
  maxConsecutiveFailures?: number;
  successRateThreshold?: number;
  latencyThreshold?: number;
  proxyStickiness?: number;
}

export class ProxyManager {
  private static instance: ProxyManager;
  private proxies: Map<string, ProxyState>;
  private providerProxyMap: Map<string, string[]>;
  private config: ProxyConfig;
  private lastCleanup: number;

  private constructor(config: ProxyConfig = {}) {
    this.config = {
      cooldownPeriod: config.cooldownPeriod || 5 * 60 * 1000, // 5 minutes
      maxConsecutiveFailures: config.maxConsecutiveFailures || 3,
      successRateThreshold: config.successRateThreshold || 0.5,
      latencyThreshold: config.latencyThreshold || 10000, // 10 seconds
      proxyStickiness: config.proxyStickiness || 10, // requests before rotation
    };

    this.proxies = new Map();
    this.providerProxyMap = new Map();
    this.lastCleanup = Date.now();
    
    this.loadProxies();
  }

  public static getInstance(config?: ProxyConfig): ProxyManager {
    if (!ProxyManager.instance) {
      ProxyManager.instance = new ProxyManager(config);
    }
    return ProxyManager.instance;
  }

  private loadProxies(): void {
    const proxyFiles = {
      'fanduel': 'src/proxies/fanduel.txt',
      'draftkings': 'src/proxies/proxies.txt',
      'caesars': 'src/proxies/caesars_only.txt',
      'hardrock': 'src/proxies/hardrock_only.txt',
      'prizepicks': 'src/proxies/prizepicks_proxies.txt',
      'default': 'src/proxies/proxies.txt',
    };

    for (const [provider, filePath] of Object.entries(proxyFiles)) {
      const fullPath = join(process.cwd(), filePath);
      if (existsSync(fullPath)) {
        const proxyList = this.parseProxyFile(fullPath);
        this.providerProxyMap.set(provider, proxyList.map(p => p.id));
        
        // Initialize proxy states
        for (const proxy of proxyList) {
          if (!this.proxies.has(proxy.id)) {
            this.proxies.set(proxy.id, proxy);
          }
        }
      }
    }

    logger.info(`Loaded ${this.proxies.size} unique proxies across all providers`);
  }

  private parseProxyFile(filePath: string): ProxyState[] {
    const content = readFileSync(filePath, 'utf-8');
    const lines = content.split('\n').filter(line => line.trim());
    
    return lines.map((line, index) => {
      const [host, port, username, password] = line.trim().split(':');
      const url = `http://${username}:${password}@${host}:${port}`;
      const id = `${host}:${port}`;
      
      return {
        id,
        url,
        successCount: 0,
        failureCount: 0,
        totalLatency: 0,
        avgLatency: 0,
        status: 'active' as const,
        lastUsedTimestamp: 0,
        consecutiveFailures: 0,
      };
    });
  }

  public async getHealthyProxy(providerId: string): Promise<ProxyState> {
    const providerProxies = this.getProviderProxies(providerId);
    const candidates = providerProxies
      .map(id => this.proxies.get(id)!)
      .filter(proxy => this.isProxyHealthy(proxy))
      .sort((a, b) => this.calculateProxyScore(b) - this.calculateProxyScore(a));

    if (candidates.length === 0) {
      // All proxies are unhealthy, try to resurrect some
      this.resurrectProxies(providerId);
      const resurrected = providerProxies
        .map(id => this.proxies.get(id)!)
        .filter(proxy => proxy.status === 'active');
      
      if (resurrected.length === 0) {
        throw new Error(`No healthy proxies available for ${providerId}`);
      }
      
      candidates.push(...resurrected);
    }

    // Select proxy based on weighted probability
    const selected = this.selectProxyByWeight(candidates);
    selected.lastUsedTimestamp = Date.now();
    
    return selected;
  }

  private getProviderProxies(providerId: string): string[] {
    const specific = this.providerProxyMap.get(providerId.toLowerCase());
    if (specific && specific.length > 0) {
      return specific;
    }
    return this.providerProxyMap.get('default') || [];
  }

  private isProxyHealthy(proxy: ProxyState): boolean {
    if (proxy.status === 'dead') {
      return false;
    }

    if (proxy.status === 'cooling_down') {
      return proxy.cooldownUntil ? Date.now() > proxy.cooldownUntil : false;
    }

    // Check success rate
    const totalRequests = proxy.successCount + proxy.failureCount;
    if (totalRequests > 10) {
      const successRate = proxy.successCount / totalRequests;
      if (successRate < this.config.successRateThreshold!) {
        return false;
      }
    }

    // Check average latency
    if (proxy.avgLatency > this.config.latencyThreshold!) {
      return false;
    }

    return true;
  }

  private calculateProxyScore(proxy: ProxyState): number {
    let score = 100;

    // Success rate component (0-50 points)
    const totalRequests = proxy.successCount + proxy.failureCount;
    if (totalRequests > 0) {
      const successRate = proxy.successCount / totalRequests;
      score += successRate * 50;
    } else {
      score += 25; // Neutral score for unused proxies
    }

    // Latency component (0-30 points)
    if (proxy.avgLatency > 0) {
      const latencyScore = Math.max(0, 30 - (proxy.avgLatency / 1000));
      score += latencyScore;
    } else {
      score += 15; // Neutral score for no latency data
    }

    // Freshness component (0-20 points)
    const timeSinceLastUse = Date.now() - proxy.lastUsedTimestamp;
    const freshnessScore = Math.max(0, 20 - (timeSinceLastUse / (60 * 1000)));
    score += freshnessScore;

    // Penalty for recent failures
    if (proxy.consecutiveFailures > 0) {
      score -= proxy.consecutiveFailures * 10;
    }

    return Math.max(0, score);
  }

  private selectProxyByWeight(candidates: ProxyState[]): ProxyState {
    const scores = candidates.map(proxy => this.calculateProxyScore(proxy));
    const totalScore = scores.reduce((sum, score) => sum + score, 0);
    
    if (totalScore === 0) {
      // All proxies have zero score, pick randomly
      return candidates[Math.floor(Math.random() * candidates.length)];
    }

    const random = Math.random() * totalScore;
    let cumulative = 0;

    for (let i = 0; i < candidates.length; i++) {
      cumulative += scores[i];
      if (random < cumulative) {
        return candidates[i];
      }
    }

    return candidates[candidates.length - 1];
  }

  public reportSuccess(proxyId: string, latency: number): void {
    const proxy = this.proxies.get(proxyId);
    if (!proxy) return;

    proxy.successCount++;
    proxy.totalLatency += latency;
    proxy.avgLatency = proxy.totalLatency / proxy.successCount;
    proxy.consecutiveFailures = 0;
    
    // Reactivate if it was cooling down
    if (proxy.status === 'cooling_down') {
      proxy.status = 'active';
      proxy.cooldownUntil = undefined;
    }
  }

  public reportFailure(proxyId: string, error: string): void {
    const proxy = this.proxies.get(proxyId);
    if (!proxy) return;

    proxy.failureCount++;
    proxy.consecutiveFailures++;
    proxy.lastFailureTimestamp = Date.now();

    // Check if proxy should be marked as dead or put in cooldown
    if (proxy.consecutiveFailures >= this.config.maxConsecutiveFailures!) {
      if (proxy.failureCount > proxy.successCount * 2) {
        proxy.status = 'dead';
        logger.warn(`Proxy ${proxyId} marked as dead due to excessive failures`);
      } else {
        proxy.status = 'cooling_down';
        proxy.cooldownUntil = Date.now() + this.config.cooldownPeriod!;
        logger.info(`Proxy ${proxyId} entering cooldown until ${new Date(proxy.cooldownUntil)}`);
      }
    }
  }

  public async rotateProxy(providerId: string): Promise<ProxyState> {
    // Simply get a new healthy proxy
    return this.getHealthyProxy(providerId);
  }

  private resurrectProxies(providerId: string): void {
    const providerProxies = this.getProviderProxies(providerId);
    const now = Date.now();

    for (const proxyId of providerProxies) {
      const proxy = this.proxies.get(proxyId);
      if (!proxy) continue;

      if (proxy.status === 'cooling_down' && proxy.cooldownUntil && now > proxy.cooldownUntil) {
        proxy.status = 'active';
        proxy.cooldownUntil = undefined;
        proxy.consecutiveFailures = 0;
        logger.info(`Resurrected proxy ${proxyId} from cooldown`);
      }
    }
  }

  public getProxyLastUsed(proxyUrl: string): number | null {
    for (const proxy of this.proxies.values()) {
      if (proxy.url === proxyUrl) {
        return proxy.lastUsedTimestamp;
      }
    }
    return null;
  }

  public async cleanup(): Promise<void> {
    const now = Date.now();
    const cleanupInterval = 60 * 60 * 1000; // 1 hour

    if (now - this.lastCleanup < cleanupInterval) {
      return;
    }

    // Resurrect dead proxies after extended period
    const resurrectAfter = 24 * 60 * 60 * 1000; // 24 hours
    for (const proxy of this.proxies.values()) {
      if (proxy.status === 'dead' && proxy.lastFailureTimestamp) {
        if (now - proxy.lastFailureTimestamp > resurrectAfter) {
          proxy.status = 'active';
          proxy.consecutiveFailures = 0;
          proxy.successCount = 0;
          proxy.failureCount = 0;
          proxy.totalLatency = 0;
          proxy.avgLatency = 0;
          logger.info(`Resurrected dead proxy ${proxy.id} after 24 hours`);
        }
      }
    }

    this.lastCleanup = now;
  }

  public getStats(): Record<string, any> {
    const stats = {
      total: this.proxies.size,
      active: 0,
      cooling_down: 0,
      dead: 0,
      avgSuccessRate: 0,
      avgLatency: 0,
    };

    let totalSuccessRate = 0;
    let totalLatency = 0;
    let proxiesWithStats = 0;

    for (const proxy of this.proxies.values()) {
      stats[proxy.status]++;
      
      const totalRequests = proxy.successCount + proxy.failureCount;
      if (totalRequests > 0) {
        totalSuccessRate += proxy.successCount / totalRequests;
        proxiesWithStats++;
      }
      
      if (proxy.avgLatency > 0) {
        totalLatency += proxy.avgLatency;
      }
    }

    if (proxiesWithStats > 0) {
      stats.avgSuccessRate = totalSuccessRate / proxiesWithStats;
      stats.avgLatency = totalLatency / proxiesWithStats;
    }

    return stats;
  }
}