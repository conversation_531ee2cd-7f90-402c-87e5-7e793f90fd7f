import { Agent, Dispatcher, request as undiciFetch } from 'undici';
import { ProxyAgent } from 'undici';
import pLimit from 'p-limit';
import { URL } from 'url';
import { lookup } from 'dns/promises';
import { logger } from '../utils/logger';
import { ProxyManager, ProxyState } from './ProxyManager';

interface RequestManagerConfig {
  globalConcurrencyLimit?: number;
  perHostConcurrencyLimit?: number;
  defaultTimeout?: number;
  keepAliveTimeout?: number;
  maxSockets?: number;
  proxyRotationThreshold?: number;
  dnsCacheTtl?: number;
}

interface DNSCacheEntry {
  addresses: string[];
  timestamp: number;
  ttl: number;
}

class RequestError extends Error {
  public statusCode?: number;
  public code?: string;
  public originalError?: Error;

  constructor(message: string, statusCode?: number, code?: string, originalError?: Error) {
    super(message);
    this.name = 'RequestError';
    this.statusCode = statusCode;
    this.code = code;
    this.originalError = originalError;
  }
}

interface RequestOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: string | Buffer;
  timeout?: number;
  retries?: number;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

export class RequestManager {
  private static instance: RequestManager;
  private agentCache: Map<string, Dispatcher>;
  private directAgent: Agent;
  private proxyManager: ProxyManager;
  private globalLimiter: pLimit.Limit;
  private hostLimiters: Map<string, pLimit.Limit>;
  private config: RequestManagerConfig;
  private requestCount: Map<string, number>;
  private lastAgentCleanup: number;
  private fixtureCache: Map<string, CacheEntry<any>>;
  private dnsCache: Map<string, DNSCacheEntry>;

  private constructor(config: RequestManagerConfig = {}) {
    this.config = {
      globalConcurrencyLimit: config.globalConcurrencyLimit || 50,
      perHostConcurrencyLimit: config.perHostConcurrencyLimit || 5,
      defaultTimeout: config.defaultTimeout || 15000,
      keepAliveTimeout: config.keepAliveTimeout || 10000, // Increased to 10s
      maxSockets: config.maxSockets || 100, // Increased connection pool
      proxyRotationThreshold: config.proxyRotationThreshold || 10,
      dnsCacheTtl: config.dnsCacheTtl || 300000, // 5 minutes DNS cache
    };

    this.agentCache = new Map();
    this.hostLimiters = new Map();
    this.requestCount = new Map();
    this.fixtureCache = new Map();
    this.dnsCache = new Map();
    this.lastAgentCleanup = Date.now();
    
    this.directAgent = new Agent({
      keepAliveTimeout: this.config.keepAliveTimeout!,
      keepAliveMaxTimeout: this.config.keepAliveTimeout! * 2,
      connections: this.config.maxSockets,
      pipelining: 1,
      connect: {
        timeout: 10000, // 10s connection timeout
      }
    });

    this.proxyManager = ProxyManager.getInstance();
    this.globalLimiter = pLimit(this.config.globalConcurrencyLimit!);
  }

  public static getInstance(config?: RequestManagerConfig): RequestManager {
    if (!RequestManager.instance) {
      RequestManager.instance = new RequestManager(config);
    }
    return RequestManager.instance;
  }

  public async fetch(providerId: string, url: string, options: RequestOptions = {}): Promise<any> {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;

    // Get or create per-host limiter
    if (!this.hostLimiters.has(hostname)) {
      this.hostLimiters.set(hostname, pLimit(this.config.perHostConcurrencyLimit || 5));
    }
    const hostLimiter = this.hostLimiters.get(hostname)!;

    return this.globalLimiter(() => 
      hostLimiter(async () => {
        const startTime = Date.now();
        let proxy: ProxyState | null = null;
        let agent: Dispatcher;
        
        try {
          // Determine if we need a proxy for this provider
          if (this.shouldUseProxy(providerId)) {
            proxy = await this.proxyManager.getHealthyProxy(providerId);
            agent = this.getOrCreateProxyAgent(proxy.url);
          } else {
            agent = this.directAgent;
          }

          // Track request count for proxy rotation
          const agentKey = proxy ? proxy.url : 'direct';
          const requestCount = (this.requestCount.get(agentKey) || 0) + 1;
          this.requestCount.set(agentKey, requestCount);

          // Rotate proxy if threshold reached
          if (proxy && requestCount >= this.config.proxyRotationThreshold!) {
            this.requestCount.set(agentKey, 0);
            const newProxy = await this.proxyManager.rotateProxy(providerId);
            proxy = newProxy;
            agent = this.getOrCreateProxyAgent(newProxy.url);
          }

          const response = await this.executeRequest(url, {
            ...options,
            dispatcher: agent,
            signal: AbortSignal.timeout(options.timeout || this.config.defaultTimeout || 15000),
          });

          // Report success to proxy manager
          if (proxy) {
            const latency = Date.now() - startTime;
            this.proxyManager.reportSuccess(proxy.id, latency);
          }

          // Periodic cleanup of stale agents
          this.cleanupStaleAgents();

          return response;
        } catch (error: any) {
          // Report failure to proxy manager
          if (proxy) {
            this.proxyManager.reportFailure(proxy.id, error.message);
            
            // Evict agent on connection errors
            if (this.isConnectionError(error)) {
              this.agentCache.delete(proxy.url);
              logger.debug(`Evicted faulty agent for proxy ${proxy.url}`);
            }
          }

          // Retry logic
          if (options.retries && options.retries > 0) {
            logger.debug(`Retrying request to ${url}, attempts remaining: ${options.retries}`);
            return this.fetch(providerId, url, { ...options, retries: options.retries - 1 });
          }

          throw error;
        }
      })
    );
  }

  private async executeRequest(url: string, options: any): Promise<any> {
    const undici = await import('undici');
    return undici.fetch(url, options);
  }

  private getOrCreateProxyAgent(proxyUrl: string): Dispatcher {
    if (!this.agentCache.has(proxyUrl)) {
      const agent = new ProxyAgent({
        uri: proxyUrl,
        keepAliveTimeout: this.config.keepAliveTimeout!,
        keepAliveMaxTimeout: this.config.keepAliveTimeout! * 2,
        connections: this.config.maxSockets,
      });
      this.agentCache.set(proxyUrl, agent);
      logger.debug(`Created new proxy agent for ${proxyUrl}`);
    }
    return this.agentCache.get(proxyUrl)!;
  }

  private shouldUseProxy(providerId: string): boolean {
    // Providers that can go direct without a proxy
    const directProviders = ['draftkings', 'betrivers'];
    if (directProviders.includes(providerId.toLowerCase())) {
        return false;
    }

    // Default to using a proxy for all other providers
    return true;
  }

  private isConnectionError(error: any): boolean {
    const connectionErrors = ['ECONNRESET', 'ETIMEDOUT', 'ECONNREFUSED', 'EHOSTUNREACH'];
    return connectionErrors.includes(error.code);
  }

  private cleanupStaleAgents(): void {
    const now = Date.now();
    const cleanupInterval = 5 * 60 * 1000; // 5 minutes

    if (now - this.lastAgentCleanup < cleanupInterval) {
      return;
    }

    // Clean up agents that haven't been used recently
    const staleThreshold = 10 * 60 * 1000; // 10 minutes
    for (const [proxyUrl, agent] of this.agentCache.entries()) {
      const lastUsed = this.proxyManager.getProxyLastUsed(proxyUrl);
      if (lastUsed && now - lastUsed > staleThreshold) {
        this.agentCache.delete(proxyUrl);
        logger.debug(`Cleaned up stale agent for ${proxyUrl}`);
      }
    }

    this.lastAgentCleanup = now;
  }

  public async destroy(): Promise<void> {
    // Close all agents
    for (const agent of this.agentCache.values()) {
      await agent.close();
    }
    await this.directAgent.close();
    this.agentCache.clear();
    this.hostLimiters.clear();
    this.requestCount.clear();
  }

  // Utility method for providers that need custom request handling
  public async batchGraphQLRequest(
    providerId: string, 
    url: string, 
    queries: any[], 
    batchSize: number = 20
  ): Promise<any[]> {
    const results: any[] = [];
    
    for (let i = 0; i < queries.length; i += batchSize) {
      const batch = queries.slice(i, i + batchSize);
      const response = await this.fetch(providerId, url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ queries: batch }),
      });
      
      const data = await response.json();
      results.push(...data);
    }
    
    return results;
  }

  // Fixture caching for BetMGM and BetRivers
  public async fetchWithCache<T>(
    cacheKey: string,
    ttlMs: number,
    fetchFn: () => Promise<T>
  ): Promise<T> {
    const cached = this.fixtureCache.get(cacheKey);
    const now = Date.now();

    if (cached && now - cached.timestamp < cached.ttl) {
      logger.debug(`Cache hit for ${cacheKey}`);
      return cached.data;
    }

    logger.debug(`Cache miss for ${cacheKey}, fetching...`);
    const data = await fetchFn();
    
    this.fixtureCache.set(cacheKey, {
      data,
      timestamp: now,
      ttl: ttlMs
    });

    // Clean up expired entries periodically
    if (this.fixtureCache.size > 100) {
      for (const [key, entry] of this.fixtureCache.entries()) {
        if (now - entry.timestamp > entry.ttl) {
          this.fixtureCache.delete(key);
        }
      }
    }

    return data;
  }

  // Clear specific cache entries
  public clearCache(pattern?: string): void {
    if (!pattern) {
      this.fixtureCache.clear();
      return;
    }

    for (const key of this.fixtureCache.keys()) {
      if (key.includes(pattern)) {
        this.fixtureCache.delete(key);
      }
    }
  }

  // DNS pre-resolution for connection optimization
  private async resolveDNS(hostname: string): Promise<string[]> {
    const cached = this.dnsCache.get(hostname);
    const now = Date.now();

    if (cached && now - cached.timestamp < cached.ttl) {
      return cached.addresses;
    }

    try {
      const { address } = await lookup(hostname);
      const addresses = [address];
      
      this.dnsCache.set(hostname, {
        addresses,
        timestamp: now,
        ttl: this.config.dnsCacheTtl!
      });

      return addresses;
    } catch (error) {
      logger.warn(`DNS resolution failed for ${hostname}:`, error);
      return cached ? cached.addresses : [];
    }
  }

  // Convenience method for standard HTTP requests
  public async request(providerId: string, url: string, options: RequestOptions = {}): Promise<any> {
    const urlObj = new URL(url);
    
    // Pre-resolve DNS to avoid blocking getaddrinfo calls
    await this.resolveDNS(urlObj.hostname);
    
    return this.fetch(providerId, url, options);
  }

  // Convenience method for GET requests
  public async get(providerId: string, url: string, options: Omit<RequestOptions, 'method'> = {}): Promise<any> {
    return this.request(providerId, url, { ...options, method: 'GET' });
  }

  // Convenience method for POST requests  
  public async post(providerId: string, url: string, body?: string | Buffer, options: Omit<RequestOptions, 'method' | 'body'> = {}): Promise<any> {
    return this.request(providerId, url, { ...options, method: 'POST', body });
  }
}