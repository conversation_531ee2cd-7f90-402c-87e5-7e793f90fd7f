import { MongoClient } from "mongodb";
require('dotenv').config();

class ProviderDatabaseClient {
    uri: string;
    clientMap: Map<any, any>;

    constructor() {
        this.uri = process.env.MONGODB_CONNECTION_STRING!;
        this.clientMap = new Map(); // Manages separate MongoClient instances for each provider
    }

    async connectToDatabase(providerName) {
        let client = this.clientMap.get(providerName);
        if (!client) {
            const options = {
                maxPoolSize: 50,
                wtimeoutMS: 5000,
                w: 0,
                journal: false,
                writeConcern: {
                    w: 0,
                    j: false,
                    wtimeout: 5000
                }
            };
            client = new MongoClient(this.uri, options);
            await client.connect();
            this.clientMap.set(providerName, client);
        }
        return client.db(process.env.DATABASE);
    }

    async storeProjections(providerName, projections, collectionName) {
        if (!projections || projections.length === 0 || !collectionName) {
            return;
        }

        const db = await this.connectToDatabase(providerName);
        const collection = db.collection(collectionName);

        try {
            // Delete with specific write concern
            await collection.deleteMany({}, {
                writeConcern: { w: 0, j: false }
            });
            
            // Insert in smaller batches with specific write concern
            const batchSize = 1000;
            for (let i = 0; i < projections.length; i += batchSize) {
                const slice = projections.slice(i, i + batchSize);
                await collection.insertMany(slice, {
                    writeConcern: { w: 0, j: false },
                    ordered: false,
                    bypassDocumentValidation: true
                });
            }
            
            console.log(
              `\x1b[32m${formatDateTime(new Date())} ` +
              `${collectionName}: ${projections.length} projections stored...\x1b[0m`
            );
        } catch (error) {
            console.error(`\x1b[31m${collectionName}: Error storing projections - `, error, '\x1b[0m');
        }
    }

    async closeConnection(providerName) {
        const client = this.clientMap.get(providerName);
        if (client) {
            await client.close();
            this.clientMap.delete(providerName);
            console.log(`${formatDateTime(new Date())} ${providerName}: MongoDB connection closed...`);
        }
    }

    async closeAllConnections() {
        for (const [providerName, client] of this.clientMap.entries()) {
            console.log(`Closing connection for ${providerName}...`);
            await client.close();
            this.clientMap.delete(providerName);
        }
    }
    
}

function formatDateTime(date) {
    const month = ('0' + (date.getMonth() + 1)).slice(-2);
    const day = ('0' + date.getDate()).slice(-2);
    const hours = ('0' + date.getHours()).slice(-2);
    const minutes = ('0' + date.getMinutes()).slice(-2);
    const seconds = ('0' + date.getSeconds()).slice(-2);

    return `[${month}-${day} ${hours}:${minutes}:${seconds}]`;
}

module.exports = new ProviderDatabaseClient();