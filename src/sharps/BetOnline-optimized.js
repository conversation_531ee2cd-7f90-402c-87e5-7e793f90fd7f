const pLimit = require('p-limit');
const { RequestManager } = require('../services/RequestManager');
const { logInfo, logError } = require('../utils/logger');

// ... (include all the existing imports and constants from BetOnline.js)

const requestManager = RequestManager.getInstance();

// Create concurrency limiter for game fetches
const gameLimit = pLimit(20); // Max 20 concurrent game fetches
const statLimit = pLimit(10); // Max 10 concurrent stat fetches per game

async function fetchBetOnline() {
  const gameInfos = await getGameIds();
  
  // Use concurrency limit for game fetching
  const projectionPromises = gameInfos.map(gameInfo => 
    gameLimit(() => fetchProjections(gameInfo))
  );
  
  const projectionResults = await Promise.all(projectionPromises);
  const allProjections = projectionResults.flat();
  
  logInfo(`[BetOnline] Fetched ${allProjections.length} total projections`);
  return allProjections;
}

async function fetchProjections(gameInfo) {
  const { gameId, league } = gameInfo;
  const projections = [];
  const endpoints = statEndpoints[league];

  if (!endpoints) {
    return projections;
  }

  // Use concurrency limit for stat endpoint fetching
  const statPromises = Object.entries(endpoints).map(([statType, urlTemplate]) =>
    statLimit(async () => {
      const url = urlTemplate.replace('${gameId}', gameId);
      try {
        // Use RequestManager for better connection pooling
        const response = await requestManager.fetch('betonline', url, {
          timeout: 30000,
          retries: 1
        });
        
        const data = await response.json();
        
        // Process the data...
        // (rest of the processing logic from original fetchProjections)
        
        return processedData;
      } catch (error) {
        logError(`[BetOnline] Error fetching ${statType} for game ${gameId}`, error);
        return [];
      }
    })
  );

  const statResults = await Promise.all(statPromises);
  return statResults.flat();
}

module.exports = { fetchBetOnline };