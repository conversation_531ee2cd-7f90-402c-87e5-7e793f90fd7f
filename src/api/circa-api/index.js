const crypto = require("crypto");
const { RequestManager } = require('../../services/RequestManager');


const CircaAPI = () => {

  const USER_AGENT = "Mozilla/5.0 (Linux; Android 7.1.2; SM-G977N Build/LMY48Z; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/75.0.3770.143 Mobile Safari/537.36";
  const cookieJar = {
    KeepBets: {
      name: "KeepBets",
      value: "false",

      KeepBets: "false"
    },

    gDetails: {
      name: "gDetails",
      value: "%5B%5D",

      gDetails: "%5B%5D"
    },
    lDetails: {
      name: "lDetails",
      value: "%5B%5D",

      lDetails: "%5B%5D"
    },

    NativeApp: {
      name: "NativeApp",
      value: "true",

      NativeApp: "true"
    },
    registration: {
      name: "registration",
      value: "no",

      registration: "no"
    },

    GvcSessionKey: {
      name: "GvcSessionKey",
      value: "",

      GvcSessionKey: ""
    },
    "ASP.NET_SessionId": {
      name: "ASP.NET_SessionId",
      value: "",

      "ASP.NET_SessionId": ""
    }

    // Missing: _ga_1PZGDRY6F5
    // Missing: _ga_3GLBP5N7D5
    // Missing: _ga
    // Missing: ASP.NET_SessionId
    // Missing: GvcSessionKey
  };


  /**
   * Generate authenticated session id
   */
  async function generateSessionId() {
    const initPayload = {
      InstallID: "",
      AppPlatform: "Android",
      AppVer: "22.08.11",
      OsName: "Android",
      OsNameLong: "11 SDK 12",
      OsMajorVer: 31,
      OsMinorVer: 0,
      DeviceModel: "Samsung"
    };
    const initRes = await fetch("https://ia.circasports.com/MobileService/api/app/AppInit", {
      method: "post",
      headers: {
        "content-type": "application/json"
      }, body: JSON.stringify(initPayload)
    });


    // Ensure ok status
    if (!initRes.ok)
      throw new Error("AppInit failed!");


    // Extract set-cookie
    const cookies = initRes.headers.getSetCookie();
    const sessionIdCookie = cookies[0].split(";")[0];

    // Get body
    const initData = await initRes.json();


    // Solve challenge
    const challengeResponseHash = crypto.createHash("sha256").update(Buffer.concat([Buffer.from(initData.ServerChallenge, "base64"), Buffer.from("=rJ[JbR[EK5.oZ0LXO;4&[kq-`.0hZSVv.YN]<Jr;W$JdJR2Du`=5&~.S2YaeI7")])).digest();
    const challengeResponse = challengeResponseHash.toString("base64");


    // AppAuthenticate
    const authPayload = {
      challengeResp: challengeResponse
    };
    const authRes = await fetch("https://ia.circasports.com/MobileService/api/app/AppAuthenticate", {
      method: "post",
      headers: {
        "content-type": "application/json",
        cookie: sessionIdCookie
      }, body: JSON.stringify(authPayload)
    });

    // Ensure ok status
    if (!authRes.ok)
      throw new Error("AppAuthenticate failed!");

    // Return session id!
    const sessionId = sessionIdCookie.slice(sessionIdCookie.indexOf("=") + 1);
    return sessionId;
  }


  /**
   * Custom fetch with middleware/header injection & cookie handling
   * @param {string} url 
   * @param {*} opts 
   */
  async function fetch(url, opts) {
    const _url = url;
    const _opts = opts;

    const targetUrl = new URL(url);

    // Inject headers, user-agent, cookies etc.
    _opts.headers = {
      // Add default/fallback headers
      accept: "application/json, text/plain, */*",
      "accept-language": "en-US,en;q=0.9",
      "user-agent": USER_AGENT,
      origin: `${targetUrl.protocol}://${targetUrl.hostname}`,

      "sec-fetch-dest": "empty",
      "sec-fetch-site": "same-origin",
      "sec-fetch-mode": "cors",

      // Re-add and override request headers if present
      ...(_opts.headers ?? {}),
    };

    // Delete any headers if the value is null-type
    for (const [key, value] of Object.entries(_opts.headers)) {
      if (value === null)
        delete _opts.headers[key];
    }

    // Append cookies from cookie jar..? Should probably check metadata of cookies like domain etc.
    _opts.headers.cookie = Object.values(cookieJar).map(cookie => ([cookie.name, cookie.value].join("="))).join("; ");


    // Get response using RequestManager
    const requestManager = RequestManager.getInstance();
    const res = await requestManager.fetch('circa', _url, {
      method: _opts.method || 'GET',
      headers: _opts.headers,
      body: _opts.body,
      timeout: 15000,
      retries: 2
    });

    //#region Update cookie jar
    // Extract cookies
    const rawSetCookies = res.headers.getSetCookie();
    // Parse cookies
    const setCookies = rawSetCookies.map(rawSetCookie => {
      const cookieName = rawSetCookie.split("=")[0];
      const rawCookieParts = rawSetCookie.split(";").map(v => v.trim());
      const cookiePartKVs = rawCookieParts.map((part) => {
        // Get first equals index
        const idxEq = part.indexOf("=");

        // Split kv string
        const [key, value] = [part.slice(0, idxEq), part.slice(idxEq + 1)];

        // Return kv as array
        return [key, value];
      });

      // Build cookie object
      const cookie = Object.fromEntries(cookiePartKVs);

      // Add cookie name field
      cookie.name = cookieName;
      // Add cookie value field
      cookie.value = cookie[cookie.name];

      // Return
      return cookie;
    });

    // Add to cookie jar!
    for (const cookie of setCookies) {
      // Update cookie jar entry
      cookieJar[cookie.name] = cookie;
    }
    //#endregion

    // Return res
    return res;
  }


  /**
   * Initialize session
   * 
   * GET /Web-Circaia
   */
  async function init(sessionKey) {
    const url = `https://ia.circasports.com/Web-Circaia/?_nativeApp=sportsw&sessionKey=${sessionKey}`;
    // Send request
    const res = await fetch(url, {
      method: "get",
      headers: {
        // Override default "accept" custom fetch value
        accept: "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",

        // Override/delete default "origin" custom fetch value
        origin: null,

        // Override default "sec-fetch-dest" custom fetch value
        "sec-fetch-dest": "document",
        // Override default "sec-fetch-site" custom fetch value
        "sec-fetch-site": "none",
        // Override default "sec-fetch-mode" custom fetch value
        "sec-fetch-mode": "navigate"
      }
    });

    // I assume this is the request that sets the "ASP.NET_SessionId" cookie.
    // Note: It seems the "GvcSessionKey" is equal to the "ASP.NET_SessioNId" cookie!

    // Note: We pass in some "sessionKey" as a query param. In a later "setSessionId" request, this same key is passed in
    //  and seems to replace our current/previous GvcSessionKey.

    // Add sessionKey to cookieJar?
    cookieJar.GvcSessionKey = {
      name: "GvcSessionKey",
      value: sessionKey,

      GvcSessionKey: sessionKey
    };

    // Get data
    const data = await res.text();

    // Return data
    return data;
  }

  /**
   * Set session id
   *
   * POST /MobileService/api/player/SetSessionId?sessionId=:sessionId
   *
   * @returns {unknown} result
   */
  async function setSessionId(sessionId) {
    // Build payload
    const payload = {};

    // Request url
    const url = `https://ia.circasports.com/MobileService/api/player/SetSessionId?sessionId=${sessionId}`;
    // Send request
    const res = await fetch(url, {
      method: "post",
      headers: {
        "content-type": "application/json",
        referer: `https://ia.circasports.com/Web-Circaia/?_nativeApp=sportsw&sessionKey=${sessionId}`
      },
      body: JSON.stringify(payload)
    });

    // Set ASP.NET_SessionId
    /*cookieJar["ASP.NET_SessionId"] = {
      value: sessionId,
      "ASP.NET_SessionId": sessionId
    };*/
    // Set GvcSessionKey
    cookieJar.GvcSessionKey = {
      ...cookieJar.GvcSessionKey,
      value: sessionId,
      GvcSessionKey: sessionId
    };

    // Get data
    const data = await res.text();

    // Return data
    return data;
  }


  /**
   * Get league groups
   * 
   * POST /MobileService/api/sports/GetLeagueGroups
   * @todo The request expects a "ASP.NET_SessionId" cookie. We need to find the request that has the set-cookie for this.
   *
   * @returns {unknown} League games result
   */
  async function getLeagueGroups() {
    // Build payload
    const payload = {};

    // Request url
    const url = "https://ia.circasports.com/MobileService/api/sports/GetLeagueGroups";
    // Send request
    const res = await fetch(url, {
      method: "post",
      headers: {
        "content-type": "application/json",
        referer: "https://ia.circasports.com/Web-Circaia/sports/sportsoddssummary?sportName=all"
      },
      body: JSON.stringify(payload)
    });

    // Get data
    const data = await res.json();

    // Return data
    return data;
  }

  /**
   * Get League games anonymously
   *
   * POST /MobileService/api/sports/getLeagueGamesAnon
   * @todo The request expects a "ASP.NET_SessionId" cookie. We need to find the request that has the set-cookie for this.
   *
   * @param {string[]} leagueIds League ids
   * @param {number} leagueId League id
   * @param {number} lineTypeId Line type id
   * @param {"moneyline"} oddsFormat Odds format
   *
   * @returns {unknown} League games result
   */
  async function getLeagueGamesAnon(
    leagueIds,
    leagueId = "",
    lineTypeId = 1,
    oddsFormat = "moneyline",
  ) {
    // Build payload
    const payload = {
      LeagueId: leagueId,
      LeagueIds: leagueIds.join(","),
      LineTypeId: lineTypeId,
      OddsFormat: oddsFormat,
    };

    // Request url
    const url =
      "https://ia.circasports.com/MobileService/api/sports/getLeagueGamesAnon";
    // Send request
    const res = await fetch(url, {
      method: "post",
      headers: {
        "content-type": "application/json",
        referer: "https://ia.circasports.com/Web-Circaia/sports/sportsoddssummary?sportName=all"
      },
      body: JSON.stringify(payload),
    });

    // Get data
    const data = await res.json();

    // Return data
    return data;
  }

  /**
   * Get game and children anonymously
   *
   * POST /MobileService/api/sports/GetGameAndChildrenAnon
   * @todo The request expects a "ASP.NET_SessionId" cookie. We need to find the request that has the set-cookie for this.
   *
   * @param {string} gameId Game id
   * @param {number} lineTypeId Line type id
   * @param {"moneyline"} oddsFormat Odds format
   *
   * @returns {unknown} Game and children result
   */
  async function getGameAndChildrenAnon(gameId, lineTypeId = 1, oddsFormat = "moneyline") {
    // Build payload
    const payload = {
      GameId: gameId,
      LineTypeId: lineTypeId,
      OddsFormat: oddsFormat
    };

    // Request url
    const url = "https://ia.circasports.com/MobileService/api/sports/GetGameAndChildrenAnon";
    // Send request
    const res = await fetch(url, {
      method: "post",
      headers: {
        "content-type": "application/json",
        referer: "https://ia.circasports.com/Web-Circaia/sports/sportsoddssummary?sportName=all"
      },
      body: JSON.stringify(payload)
    });

    // Get data
    const data = await res.json();

    // Return data
    return data;
  }


  return {
    generateSessionId,
    setSessionId,

    getLeagueGroups,
    getLeagueGamesAnon,
    getGameAndChildrenAnon
  };
};

module.exports = CircaAPI;