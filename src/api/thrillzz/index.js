const WebSocket = require('ws');
const { RequestManager } = require('../../services/RequestManager');
const fs = require('fs');
const path = require('path');

function convertToAmericanOdds(decimalOdds) {
    if (decimalOdds >= 2) {
        return `+${Math.round((decimalOdds - 1) * 100)}`;
    } else {
        return `${Math.round(-100 / (decimalOdds - 1))}`;
    }
}

class ThrillzzApi {
    constructor() {
        this.url = 'wss://sc.thrillzsystem.com/ws';
        this.headers = {
            'Pragma': 'no-cache',
            'Origin': 'https://app.thrillzz.com',
            'Accept-Language': 'en-US,en;q=0.9,la;q=0.8,en-GB;q=0.7',
            'Sec-WebSocket-Key': 'jUp/tNEv5shFxD1hvDQRKw==',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'Upgrade': 'websocket',
            'Cache-Control': 'no-cache',
            'Connection': 'Upgrade',
            'Sec-WebSocket-Version': '13',
            'Sec-WebSocket-Extensions': 'permessage-deflate; client_max_window_bits'
        };

        this.leagues = {
            MLB: 183,
            // Add more leagues as needed
        };

        this.ws = null;
        this.eventQueue = [];
        this.currentEventIndex = 0;
        this.projections = [];
        this.resolve = null;

        this.requestManager = RequestManager.getInstance();
    }


    connect() {
        return new Promise((resolve) => {
            this.resolve = resolve;
            for (let leagueName in this.leagues) {
                const leagueId = this.leagues[leagueName];
                this.connectToLeague(leagueId, leagueName);
            }
        });
    }

    connectToLeague(leagueId, leagueName) {
        // Note: WebSocket proxy support through RequestManager would require additional implementation
        // For now, we'll use direct connection (WebSocket proxy support is complex)
        this.ws = new WebSocket(this.url, { headers: this.headers });

        this.ws.on('open', () => this.onOpen(leagueId, leagueName));
        this.ws.on('message', (data) => this.onMessage(data, leagueName));
        this.ws.on('error', (error) => this.onError(error, leagueName));
        this.ws.on('close', () => this.onClose(leagueName));
    }

    onOpen(leagueId, leagueName) {
        //console.log(`WebSocket connection opened for ${leagueName}`);

        const initialMessage = {
            type: "subscribeLive",
            data: {
                subscribe: true,
                type: 4,
                isLive: false,
                id: leagueId
            }
        };

        this.ws.send(JSON.stringify(initialMessage));
        //console.log(`Initial message sent for ${leagueName}:`, JSON.stringify(initialMessage));
    }

    onMessage(data, leagueName) {
        try {
            if (Buffer.isBuffer(data)) {
                const message = data.toString('utf-8');
                const jsonData = JSON.parse(message);

                if (this.currentEventIndex === 0) {
                    this.eventQueue = jsonData.data.payload.map(event => ({
                        eventName: event.name,
                        eventId: event.id,
                        startTime: new Date(event.date)
                    }));

                    //console.log(`${leagueName} Event Queue:`, this.eventQueue);
                } else {
                    const currentEvent = this.eventQueue[this.currentEventIndex - 1];
                    const eventProjections = this.extractProjections(jsonData.data.payload, leagueName, currentEvent.eventName, currentEvent.startTime);
                    this.projections.push(...eventProjections);
                }

                if (this.currentEventIndex < this.eventQueue.length) {
                    const nextEvent = this.eventQueue[this.currentEventIndex];
                    const eventSubscriptionMessage = {
                        type: "subscribeLive",
                        data: {
                            subscribe: true,
                            type: 1,
                            isLive: false,
                            id: nextEvent.eventId
                        }
                    };

                    this.ws.send(JSON.stringify(eventSubscriptionMessage));
                    //console.log(`Subscription message sent for ${leagueName} event ID ${nextEvent.eventId}:`, JSON.stringify(eventSubscriptionMessage));
                    this.currentEventIndex++;
                } else {
                    this.ws.close();
                    //console.log(`WebSocket connection for ${leagueName} closed after processing all events.`);
                    this.resolve(this.projections);
                }
            } else {
                console.log(`[THRILLZZ] Received non-buffer message for ${leagueName}:`, data.toString('utf-8'));
            }
        } catch (err) {
            console.error(`[THRILLZZ] Error processing message for ${leagueName}:`, err);
            this.ws.close();
            console.log(`[THRILLZZ] WebSocket connection for ${leagueName} closed due to an error.`);
        }
    }

    extractProjections(eventData, leagueName, eventName, startTime) {
        const projections = [];
    
        for (const marketId in eventData.markets) {
            const market = eventData.markets[marketId];
    
            // Filter the markets that start with "Under/Over Player" or "Under/Over Pitcher"
            if (market.name.startsWith('Under/Over Player') || market.name.startsWith('Under/Over Pitcher')) {
                const playerSelections = {};
    
                for (const selectionId in market.selections) {
                    const selection = market.selections[selectionId];
    
                    // Only include selections that meet the criteria
                    if (!selection.isSuspended && selection.isVisible && !market.isLiveMarket) {
                        const playerName = selection.playerName;
                        const line = selection.line;
    
                        if (!playerSelections[playerName]) {
                            playerSelections[playerName] = [];
                        }
    
                        playerSelections[playerName].push(selection);
                    }
                }
    
                // Check if we have both over and under for each player
                for (const playerName in playerSelections) {
                    const selections = playerSelections[playerName];
    
                    if (selections.length === 2) {
                        const overSelection = selections.find(s => s.n === "Over");
                        const underSelection = selections.find(s => s.n === "Under");
    
                        // Ensure both selections have the same baseline and player name
                        if (overSelection && underSelection && overSelection.line === underSelection.line && overSelection.playerName === underSelection.playerName) {
                            const projection = {
                                proj_id: `${overSelection.id}_${underSelection.id}`,
                                league: leagueName,
                                player_name: playerName,
                                stat_type: market.name,
                                line: parseFloat(overSelection.line),
                                over_odds_decimal: overSelection.price,
                                under_odds_decimal: underSelection.price,
                                over_odds_american: convertToAmericanOdds(overSelection.price),
                                under_odds_american: convertToAmericanOdds(underSelection.price),
                                matchup: eventName,
                                start_time: startTime,
                                source: 'Thrillzz'
                            };
    
                            projections.push(projection);
                        }
                    }
                }
            }
        }
    
        return projections;
    }    

    onError(error, leagueName) {
        console.error(`[THRILLZZ] WebSocket error for ${leagueName}:`, error);
        this.ws.close();
        console.log(`[THRILLZZ] WebSocket connection for ${leagueName} closed due to an error.`);
    }

    onClose(leagueName) {
        console.log(`[THRILLZZ] WebSocket connection closed for ${leagueName}`);
    }
}

module.exports = ThrillzzApi;
