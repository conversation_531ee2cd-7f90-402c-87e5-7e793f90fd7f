// fanduel-sharp-api/index.js

const { got } = require('got-cjs');
const { HttpsProxyAgent } = require('https-proxy-agent');
const { join } = require('path');
const fs = require('fs').promises;

/**
 * Loads proxies from a given file path and returns them as an array of strings.
 * Each line in the file is assumed to be a single proxy in the format "ip:port:user:pass".
 *
 * @param {string} filePath - The file path of the proxies.txt file
 * @returns {Promise<string[]>} - Resolves with an array of proxy lines
 */
async function loadProxies(filePath) {
    try {
        const data = await fs.readFile(filePath, 'utf-8');
        return data.trim().split('\n').filter(line => line.length > 0); // Filter out empty lines
    } catch (error) {
        console.error(`[FANDUEL] Error loading proxies from ${filePath}:`, error);
        return []; // Return empty array on error
    }
}

/**
 * Formats an integer representing American odds into a string. Prepends "+" for positive odds,
 * leaves "-" as is, or returns "EVEN" if the integer is 0. Returns "N/A" if invalid.
 *
 * @param {number|string|null|undefined} americanOdds - The American odds integer or string from the feed
 * @returns {string} - A formatted string e.g. "+115", "-140", or "N/A"
 */
function formatOdds(americanOdds) {
    if (americanOdds === null || americanOdds === undefined) {
        return "N/A"; // Return "N/A" if odds are not available
    }

    const oddsValue = parseInt(americanOdds);
    if (isNaN(oddsValue)) {
        return "N/A"; // Return "N/A" if oddsValue is not a valid number
    }

    if (oddsValue > 0) {
        return `+${oddsValue}`;
    } else if (oddsValue < 0) {
        return oddsValue.toString(); // Keep negative odds as is
    } else {
        // Handle 0 or "0" explicitly if needed, though unlikely for standard odds
        return "EVEN"; // Or perhaps "N/A" depending on context
    }
}

/**
 * FanduelSharpAPI handles searching Fanduel's public search endpoint for markets, rotating proxies
 * to avoid blocks. Once the raw data is fetched, it processes the data into standardized
 * "projection" objects for each Over/Under or specialized market.
 */
class FanduelSharpAPI {
    /**
     * @constructor
     * @param {string} proxyFilePath - Path to a file containing proxies (one per line)
     */
    constructor(proxyFilePath) {
        this.proxyFilePath = proxyFilePath || join(__dirname, 'proxies.txt');
        this.proxies = [];
        this.currentProxyIndex = 0;
        this.lastProxyUseTime = Date.now();
        this._lastRawMarkets = null;
    }

    /**
     * Loads proxies into memory. This must be called before making requests.
     */
    async init() {
        this.proxies = await loadProxies(this.proxyFilePath);
        if (this.proxies.length === 0) {
            console.error("[FANDUEL] No proxies loaded. API calls will likely fail.");
        }
        this.currentProxyIndex = 0; // Reset index when initializing
    }

    /**
     * Retrieves the next proxy in the list, resetting when the end is reached.
     * Implements a small delay if the last proxy usage was too recent.
     *
     * @returns {Promise<string|null>} - The next proxy string or null if no proxies loaded
     */
    async getNextProxy() {
        if (this.proxies.length === 0) {
            console.warn("[FANDUEL] Attempted to get proxy, but none are loaded.");
            return null;
        }

        if (this.currentProxyIndex >= this.proxies.length) {
            const timeSinceLastUse = Date.now() - this.lastProxyUseTime;
            const delayNeeded = 2000 - timeSinceLastUse;
            if (delayNeeded > 0) {
                // console.log(`[FANDUEL] Cycling proxies, waiting ${delayNeeded}ms`);
                await new Promise(resolve => setTimeout(resolve, delayNeeded));
            }
            this.currentProxyIndex = 0; // Reset index
        }

        const proxy = this.proxies[this.currentProxyIndex];
        this.currentProxyIndex++;
        this.lastProxyUseTime = Date.now();
        return proxy;
    }

    /**
     * Creates an HTTPS proxy agent from a proxy string in the format "ip:port:user:pass".
     *
     * @param {string} proxy - A single line from the proxies array
     * @returns {HttpsProxyAgent|null} - The configured HttpsProxyAgent or null if proxy format is invalid
     */
    createProxyAgent(proxy) {
        if (!proxy) return null;
        const parts = proxy.split(':');
        if (parts.length !== 4) {
            console.error(`[FANDUEL] Invalid proxy format: ${proxy}`);
            return null;
        }
        const [ip, port, username, password] = parts;
        const proxyUrl = `http://${username}:${password}@${ip}:${port}`;
        return new HttpsProxyAgent(proxyUrl);
    }

    /**
     * Fetches JSON from a Fanduel endpoint using a rotating proxy approach. Retries on 403/429 errors.
     */
     async fetchDetailsWithProxy(url, retryCount = 0, maxRetries = 5, initialDelay = 2000) {
        let agent = null;
        try {
            const proxy = await this.getNextProxy();
            if (!proxy) {
                console.error("[FANDUEL] No proxy available for request.");
                return null;
            }
            agent = this.createProxyAgent(proxy);
            if (!agent) {
                console.error("[FANDUEL] Failed to create proxy agent.");
                // Optionally, try the next proxy immediately if agent creation fails
                 if (retryCount < maxRetries) {
                    return this.fetchDetailsWithProxy(url, retryCount + 1, maxRetries, initialDelay);
                 } else {
                    return null;
                 }
            }

            // console.log(`[FANDUEL] Attempting fetch with proxy index ${this.currentProxyIndex - 1}`); // Debug log

            const response = await got(url, {
                headers: {
                    'authority': 'search.sportsbook.fanduel.com',
                    'accept': 'application/json',
                    'accept-language': 'en-US,en;q=0.9',
                    'origin': 'https://sportsbook.fanduel.com',
                    'referer': 'https://sportsbook.fanduel.com/',
                    'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': '"macOS"',
                    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
                    'x-sportsbook-region': 'NC' // Or appropriate region
                },
                agent: { https: agent },
                responseType: 'json',
                throwHttpErrors: false,
                timeout: { request: 15000 }
            });

            // --- Status Code Handling ---
            if (response.statusCode === 403 || response.statusCode === 429) {
                console.warn(`[FANDUEL] Received ${response.statusCode}, retry ${retryCount + 1}/${maxRetries}... Proxy: ${proxy.split(':')[0]}`);
                if (retryCount < maxRetries) {
                    const delay = initialDelay * Math.pow(2, retryCount);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    return this.fetchDetailsWithProxy(url, retryCount + 1, maxRetries, initialDelay);
                } else {
                    console.error(`[FANDUEL] Max retries (${maxRetries}) reached for ${response.statusCode} error. URL: ${url}`);
                    return null;
                }
            } else if (response.statusCode >= 400) {
                 console.error(`[FANDUEL] HTTP Error ${response.statusCode}: ${response.statusMessage}. URL: ${url}. Body: ${response.body ? JSON.stringify(response.body).substring(0, 200) : 'N/A'}`);
                 return null;
            } else if (response.statusCode === 200) {
                 return response.body; // Success
            } else {
                 console.warn(`[FANDUEL] Unexpected status code ${response.statusCode}. URL: ${url}`);
                 return null;
            }

        } catch (error) {
            console.error(`[FANDUEL] Network/Request error during fetch: ${error.message}. URL: ${url}`);
            if (retryCount < 2) { // Retry fewer times for general errors
                 await new Promise(resolve => setTimeout(resolve, 3000)); // Fixed delay
                 return this.fetchDetailsWithProxy(url, retryCount + 1, 2, initialDelay);
            }
            return null;
        }
    }


    /**
     * Main function to fetch all relevant markets from Fanduel.
     */
     async fetchFanduel() {
        let allMarkets = new Map();
        let allEvents = new Map();
        let allCompetitions = new Map();
        let totalQueriesProcessed = 0;
        let totalQueriesWithResults = 0;

        console.log('[FANDUEL] Starting market collection...');

        // --- Restored Original searchQueries ---
        const searchQueries = [
            {
                sport: 'Basketball',
                queries: ['points', 'rebounds', 'assists', 'threes', 'Pts', 'Reb'],
                pageSize: 20
            },
            {
                sport: 'Baseball',
                queries: ['strikeout', 'outs', 'to hit a single', 'to hit a double', 'to hit a triple', 'to hit a home run'],
                pageSize: 20
            },
            /*
            {
                sport: 'American Football',
                queries: ['rush', 'pass', 'receiving', 'reception', 'touchdown'],
                pageSize: 20
            },
            {
                sport: 'Ice Hockey',
                queries: ['points', 'shots', 'Any Time Goal Scorer'],
                pageSize: 20
            },
            {
                sport: 'Soccer',
                queries: ['shots', 'passes'],
                pageSize: 20
            },
            */
            // Specifically for Golf
            {
                sport: 'Golf',
                queries: ['round score', 'birdies'],
                pageSize: 20
            }
        ];
        // --- End Restoration ---

        try {
            await this.init(); // Load proxies

            for (const queryGroup of searchQueries) {
                const batchSize = 5; // Process queries in smaller batches
                for (let i = 0; i < queryGroup.queries.length; i += batchSize) {
                    const batch = queryGroup.queries.slice(i, i + batchSize);
                    console.log(`[FANDUEL] Processing batch ${Math.floor(i/batchSize) + 1} for ${queryGroup.sport}: ${batch.join(', ')}`);

                    const batchPromises = batch.map(async query => {
                        let startIndex = 0;
                        let hasMoreResults = true;
                        let pageCount = 0;
                        const maxPages = 5; // Limit pages per query
                        let queryFoundResults = false;

                        while (hasMoreResults && pageCount < maxPages) {
                            pageCount++;
                            const baseUrl = 'https://search.sportsbook.fanduel.com/search/tabs';
                            const fullBaseUrl = queryGroup.sport ? `${baseUrl}/${encodeURIComponent(queryGroup.sport)}` : baseUrl;

                            const params = new URLSearchParams({
                                q: query,
                                startIndex: startIndex.toString(),
                                _ak: 'FhMFpcPWXMeyZxOx',
                                isDesktop: 'true',
                                expandedEvents: '50',
                                timezone: 'America/New_York',
                                withMarkets: 'true',
                                includeMarketResults: 'true',
                                includeMarketBlurbs: 'false'
                            });

                            const url = `${fullBaseUrl}?${params.toString()}`;
                            const data = await this.fetchDetailsWithProxy(url);

                            if (data?.response?.attachments) {
                                const { markets, events, competitions } = data.response.attachments;
                                const marketCount = Object.keys(markets || {}).length;

                                if (marketCount > 0) {
                                    if (!queryFoundResults) {
                                        queryFoundResults = true;
                                        totalQueriesWithResults++;
                                    }
                                    Object.entries(markets || {}).forEach(([id, market]) => allMarkets.set(id, market));
                                    Object.entries(events || {}).forEach(([id, event]) => allEvents.set(id, event));
                                    Object.entries(competitions || {}).forEach(([id, comp]) => allCompetitions.set(id, comp));

                                    hasMoreResults = marketCount >= queryGroup.pageSize;
                                    startIndex += queryGroup.pageSize;
                                } else {
                                    hasMoreResults = false;
                                }
                            } else {
                                console.warn(`[FANDUEL] No data or attachments received for query "${query}", page ${pageCount}.`);
                                hasMoreResults = false;
                            }
                            await new Promise(resolve => setTimeout(resolve, 500)); // Small delay between pages
                        }
                        if (pageCount >= maxPages && hasMoreResults) {
                            console.warn(`[FANDUEL] Reached max pages (${maxPages}) for query "${query}", potentially missing data.`);
                        }
                        totalQueriesProcessed++;
                    }); // End map

                    await Promise.all(batchPromises);
                    await new Promise(resolve => setTimeout(resolve, 1000)); // Delay between batches
                } // End batch loop
            } // End queryGroup loop

            console.log(`[FANDUEL] Market collection complete - ${totalQueriesWithResults}/${totalQueriesProcessed} queries returned results`);
            console.log(`[FANDUEL] Total collected - Markets: ${allMarkets.size}, Events: ${allEvents.size}, Competitions: ${allCompetitions.size}`);

        } catch (error) {
            console.error('[FANDUEL] Error during market collection:', error);
        }

        const data = {
            attachments: {
                markets: Object.fromEntries(allMarkets),
                events: Object.fromEntries(allEvents),
                competitions: Object.fromEntries(allCompetitions)
            }
        };

        this._lastRawMarkets = data.attachments.markets;
        return this.extractProjections(data);
    }

    /**
     * Getter function for debugging raw markets after fetchFanduel().
     */
    debugGetRawMarkets() {
        return this._lastRawMarkets;
    }

    /**
     * Extracts projections for one-sided player prop markets like "To Hit A Home Run".
     * Iterates through each player (runner) in the market.
     */
    extractHittingPropProjection(market, events, competitions) {
        const projections = [];
        try {
            if (!market || !market.runners || market.runners.length < 1 || !market.marketName) {
                // console.warn(`[FD HITTING PROP SKIP] Invalid market structure for ${market?.marketId || 'unknown market'}.`);
                return projections; // Need runners and a market name
            }

            const event = events[market.eventId];
            const competition = competitions[market.competitionId];
            let league = competition ? competition.name : 'Unknown League';

            // Standardize league names if needed
            if (league === 'MLB Games' || league === 'MLB') league = 'MLB';
            else if (league === 'NCAA Baseball') league = 'NCAAB'; // Or a specific code you use

            // Determine Stat Type and Line from Market Name/Type
            let statType = null;
            let line = null;
            const marketNameLower = market.marketName.toLowerCase();
            const marketTypeLower = market.marketType?.toLowerCase(); // Handle potential undefined marketType

            // --- Logic to Map Market to Stat Type and Line ---
            if (marketTypeLower === 'to_hit_a_home_run' || marketNameLower === 'to hit a home run') {
                statType = 'Home Runs';
                line = 0.5;
            } else if (marketNameLower.includes('to record') && marketNameLower.includes(' hit')) {
                statType = 'Hits';
                if (marketNameLower.includes('2+')) line = 1.5;
                else if (marketNameLower.includes('3+')) line = 2.5;
                else if (marketNameLower.includes('4+')) line = 3.5;
                else line = 0.5; // Default for "to record a hit"
            } else if (marketNameLower.includes('to hit') && marketNameLower.includes(' single')) {
                statType = 'Singles';
                if (marketNameLower.includes('2+')) line = 1.5;
                else if (marketNameLower.includes('3+')) line = 2.5;
                else line = 0.5; // Default for "to hit a single"
            } else if (marketNameLower.includes('to hit') && marketNameLower.includes(' double')) {
                statType = 'Doubles';
                if (marketNameLower.includes('2+')) line = 1.5;
                else line = 0.5; // Default for "to hit a double"
            } else if (marketNameLower.includes('to hit') && marketNameLower.includes(' triple')) {
                statType = 'Triples';
                line = 0.5; // Usually only "to hit a triple"
            } else if (marketNameLower.includes('to record') && marketNameLower.includes(' total base')) {
                statType = 'Total Bases';
                if (marketNameLower.includes('2+')) line = 1.5;
                else if (marketNameLower.includes('3+')) line = 2.5;
                else if (marketNameLower.includes('4+')) line = 3.5;
                else if (marketNameLower.includes('5+')) line = 4.5;
                else line = 0.5; // Default for "to record 1+ total base" or similar
            } else if (marketNameLower.includes('to record') && marketNameLower.includes(' rbi')) {
                statType = 'RBIs';
                if (marketNameLower.includes('2+')) line = 1.5;
                else if (marketNameLower.includes('3+')) line = 2.5;
                else line = 0.5; // Default for "to record an rbi"
            } else if (marketNameLower.includes('to record') && marketNameLower.includes(' run')) {
                statType = 'Runs Scored';
                if (marketNameLower.includes('2+')) line = 1.5;
                else line = 0.5; // Default for "to record a run"
            }
            // Add more else if blocks for other types as needed

            // If we couldn't determine stat/line, skip this market
            if (statType === null || line === null) {
                // console.warn(`[FD HITTING PROP SKIP] Could not determine stat/line for market: ${market.marketName} (ID: ${market.marketId})`);
                return projections;
            }

            let startTime = null; // Handle date safely
            try {
                if (market.marketTime) {
                    startTime = new Date(market.marketTime);
                    if (isNaN(startTime.getTime())) startTime = null;
                } else if (event && event.openDate) {
                    startTime = new Date(event.openDate);
                    if (isNaN(startTime.getTime())) startTime = null;
                }
            } catch (e) { startTime = null; }

            if (!startTime) {
                // console.error(`[FD HITTING PROP SKIP] Cannot determine valid start time for market ${market.marketId}. Skipping.`);
                return projections; // Cannot proceed without time
            }

            // Iterate through each player (runner) in this market
            for (const runner of market.runners) {
                if (!runner || !runner.runnerName || !runner.winRunnerOdds) {
                    continue; // Skip runner if essential data is missing
                }

                const playerName = runner.runnerName.includes('(')
                    ? runner.runnerName.substring(0, runner.runnerName.indexOf('(')).trim()
                    : runner.runnerName.trim();

                const winOdds = runner.winRunnerOdds;
                const overOddsAmericanRaw = winOdds.americanDisplayOdds?.americanOdds ?? winOdds.americanDisplayOdds?.americanOddsInt;
                const overOddsDecimal = winOdds.trueOdds?.decimalOdds?.decimalOdds;

                // Validate odds
                if (overOddsAmericanRaw == null || overOddsDecimal == null) {
                    // console.warn(`[FD HITTING PROP SKIP] Missing odds for runner ${playerName} in market ${market.marketId}.`);
                    continue;
                }

                const formattedOverOdds = formatOdds(overOddsAmericanRaw);
                if (formattedOverOdds === 'N/A') {
                    // console.warn(`[FD HITTING PROP SKIP] Invalid formatted odds for runner ${playerName} in market ${market.marketId}.`);
                    continue;
                }

                // Construct the projection object for this player
                const projectionData = {
                    proj_id: `${market.marketId}_${runner.selectionId}`, // Unique ID per player/market
                    player_name: playerName,
                    stat_type: statType,
                    line: line,
                    over_odds_american: formattedOverOdds,
                    under_odds_american: null, // Explicitly null for one-sided
                    over_odds_decimal: overOddsDecimal,
                    under_odds_decimal: null, // Explicitly null for one-sided
                    league: league,
                    matchup: event?.name || 'Unknown Game',
                    start_time: startTime, // Keep as Date object
                    is_alt: false, // Assume these are main lines unless specified otherwise
                    source: 'Fanduel'
                };
                projections.push(projectionData);
            } // End runner loop

            return projections;

        } catch (error) {
            console.error(`[FD ERROR] Error in extractHittingPropProjection for market ${market?.marketId}:`, error);
            return projections; // Return whatever was processed before the error
        }
    }

    /**
     * Top-level function to convert raw Fanduel data into standardized projections.
     */
    extractProjections(data) {
        if (
            !data?.attachments?.markets ||
            !data?.attachments?.events ||
            !data?.attachments?.competitions
        ) {
            console.warn('[FANDUEL] Missing required data in extractProjections.');
            return [];
        }
    
        const { markets, events, competitions } = data.attachments;
        console.log('[FANDUEL] Starting projection extraction...');
    
        const marketGroups = new Map(); // Still used for O/U props
        const finalProjections = []; // Array to hold all final projection objects
        let processedMarkets = 0;
        let skippedDirectly = 0;
        let oneSidedCount = 0;
        let groupedCount = 0;
    
        // Define known one-sided market types/name patterns
        const oneSidedMarketTypes = new Set([
            'TO_HIT_A_HOME_RUN',
            // Add other specific Fanduel marketType strings if you identify them
            'PLAYER_TO_RECORD_A_HIT', // Example, confirm actual type
            'TO_RECORD_HITS_ALTERNATE' // Example, confirm actual type
        ]);
        const oneSidedNamePatterns = [
            /to hit (a|\d+\+) (single|double|triple)/i,
            /to record (a|\d+\+) (hit|total base|rbi|run)/i,
            // Add more REGEX patterns if needed
        ];
    
        // --- Main Market Loop ---
        for (const marketId in markets) {
            processedMarkets++;
            const market = markets[marketId];
    
            if (!market?.marketName || !market.runners || market.runners.length === 0) {
                skippedDirectly++;
                continue;
            }
    
            const marketNameLower = market.marketName.toLowerCase();
            const marketTypeLower = market.marketType?.toLowerCase();
    
            // --- Check if it's a one-sided hitting prop ---
            let isOneSided = oneSidedMarketTypes.has(market.marketType); // Check type first
            if (!isOneSided) { // If type didn't match, check name patterns
                isOneSided = oneSidedNamePatterns.some(pattern => pattern.test(marketNameLower));
            }
    
            if (isOneSided && !marketNameLower.includes('players to combine')) { // Ensure it's not a combined player prop
                oneSidedCount++;
                const hittingProjs = this.extractHittingPropProjection(market, events, competitions);
                if (hittingProjs && hittingProjs.length > 0) {
                    finalProjections.push(...hittingProjs);
                }
                continue; // Go to the next market, skip grouping
            }
    
            // --- Handle Golf Markets (if needed, similar routing as hitting props) ---
            // Example:
            // if (market.marketType === 'SELECTED_ROUND_SCORES_IMG' || /* other golf types */) {
            //     const golfProjs = this.extractGolfProjection(market, events, competitions); // Hypothetical function
            //     if (golfProjs && golfProjs.length > 0) {
            //         finalProjections.push(...golfProjs);
            //     }
            //     continue;
            // }
    
    
            // --- ELSE: Assume Over/Under or other groupable type ---
            groupedCount++;
            const { playerName, statType } = this.parseMarketName(
                market.marketName,
                market.marketType
            );
    
            if (!playerName || !statType || this.isThresholdMarket(market) /* Keep threshold check */ ) {
                skippedDirectly++;
                continue; // Skip if parsing fails or it's a threshold type we ignore
            }
    
            const baseStatType = statType;
            const key = `${playerName}-${baseStatType}`;
    
            if (!marketGroups.has(key)) {
                marketGroups.set(key, []);
            }
            marketGroups.get(key).push(market);
    
        } // --- End Market Loop ---
    
        console.log(
            `[FANDUEL] Market Processing - Total: ${processedMarkets}, ` +
            `One-Sided: ${oneSidedCount}, Grouped: ${groupedCount}, Skipped Directly: ${skippedDirectly}`
        );
    
        // --- Process Grouped Markets ---
        let validGroupedProjections = 0;
        let skippedGroupedProjections = 0;
    
        for (const [key, groupMarkets] of marketGroups) {
            const idx = key.lastIndexOf('-');
            if (idx === -1) continue;
            const playerName = key.substring(0, idx);
            const baseStatType = key.substring(idx + 1);
    
            for (const market of groupMarkets) {
                // Use the original extractOverUnderProjection for these
                // Make sure it handles potential null returns correctly
                const projItems = this.extractOverUnderProjection(market, playerName, baseStatType, events, competitions);
    
                if (Array.isArray(projItems)) { // Handle cases where it might return an array (though less likely now)
                     projItems.forEach(item => {
                        if(item) {
                            finalProjections.push(item);
                            validGroupedProjections++;
                        } else {
                            skippedGroupedProjections++;
                        }
                     });
                } else if (projItems) { // Handles single object return
                    finalProjections.push(projItems);
                    validGroupedProjections++;
                } else { // Handles null return
                    skippedGroupedProjections++;
                }
            }
        }
    
        console.log(
            `[FANDUEL] Grouped Projection Processing - Valid: ${validGroupedProjections}, Skipped: ${skippedGroupedProjections}`
        );
        console.log(`[FANDUEL] Total Final Projections: ${finalProjections.length}`);
    
        return finalProjections;
    }

    /**
     * Attempt to parse a market name into { playerName, statType }.
     * Handles various formats including pitcher outs and golf specifics.
     */
     parseMarketName(marketName, marketType) { // Renamed internal variable to avoid conflict
        const result = { playerName: '', statType: '' };
        // console.log(`[FD DEBUG PARSE INPUT] Name: "${marketName}", Type: "${marketType}"`);

        // 1. Specific Market Type Overrides (Highest Priority)
        if (marketType === 'PITCHER_B_OUTS_RECORDED_SB') {
            // Extract player name by removing " Outs Recorded" from the end
            const potentialPlayerName = marketName.replace(/\s+Outs Recorded$/i, '').trim();
            if (potentialPlayerName && potentialPlayerName !== marketName) { // Check if replacement happened
                result.playerName = potentialPlayerName;
                result.statType = 'Outs Recorded'; // Consistent internal name
                // console.log(`[FD DEBUG PARSE SPECIFIC] Pitcher Outs Matched: Player="${result.playerName}", Stat="${result.statType}"`);
                return result;
            } else {
                console.warn(`[FD DEBUG PARSE SPECIFIC] Failed to extract player name for Pitcher Outs: "${marketName}"`);
            }
        } else if (marketType === 'SELECTED_ROUND_SCORES_IMG') {
            const match = marketName.match(/-\s*(.+)$/);
            if (match && match[1]) {
                result.playerName = match[1].trim();
                result.statType = 'Strokes'; // Standardized name
                // console.log(`[FD DEBUG PARSE SPECIFIC] Golf Strokes Matched: Player="${result.playerName}", Stat="${result.statType}"`);
                return result;
            }
        } else if (marketType === 'NUMBER_OF_BIRDIES_(OR_BETTER)_IN_THE_ROUND_IMG') {
            const match = marketName.match(/-\s*(.+)$/);
            if (match && match[1]) {
                result.playerName = match[1].trim();
                result.statType = 'Birdies or Better'; // Standardized name
                // console.log(`[FD DEBUG PARSE SPECIFIC] Golf Birdies Matched: Player="${result.playerName}", Stat="${result.statType}"`);
                return result;
            }
        } else if (marketType === 'HOLE_SCORE_IMG') {
             const match = marketName.match(/^(.+?)\s+-\s*(.+)$/); // "Hole Score (...) - Player Name"
             if(match && match[1] && match[2]) {
                 result.statType = match[1].trim(); // Keep specific hole info for now
                 result.playerName = match[2].trim();
                 // console.log(`[FD DEBUG PARSE SPECIFIC] Hole Score Matched: Player="${result.playerName}", Stat="${result.statType}"`);
                 return result;
             }
        }

        // 2. Try the standard "Player Name - Stat Type" pattern (if not handled above)
        const traditionalMatch = marketName.match(/^(.+?)\s+-\s+(.+?)$/);
        if (traditionalMatch) {
            result.playerName = traditionalMatch[1].trim();
            result.statType = traditionalMatch[2].trim();
            // console.log(`[FD DEBUG PARSE TRADITIONAL] Market: "${marketName}", Player="${result.playerName}", Stat="${result.statType}"`);
            return result;
        }

        // 3. If nothing matched
        // console.warn(`[FD DEBUG PARSE FAIL] Could not parse market name: "${marketName}", Type: "${marketType}"`);
        return result; // Return empty result
    }


    /**
     * Determines if a market might be a threshold market (like "To Record 2+ Birdies").
     * We skip these for simpler Over/Under logic.
     */
    isThresholdMarket(market) {
        // Added check for undefined or null marketName
        if (!market || !market.marketName) return false;
        return market.marketName.includes('To Record');
    }

    /**
     * Extract Over/Under lines from a standard 2-runner market or specific types like Golf Scores/Birdies/Pitcher Outs.
     */
    extractOverUnderProjection(market, playerName, statType, events, competitions) { // Added market parameter back
        // --- Ensure this log is the FIRST line ---
        if (market.marketType === 'PITCHER_B_OUTS_RECORDED_SB') {
            // console.log(`[FD DEBUG ENTRY] Entering extractOverUnderProjection for Market ID: ${market.marketId}, Initial Stat Type: ${statType}`);
        }
        // --- END ---

        try {
            if (!market.runners || market.runners.length < 2) {
                return null;
            }

            const event = events[market.eventId];
            const competition = competitions[market.competitionId];
            let league = competition ? competition.name : 'Unknown League';

            // --- Corrected Market Type Check ---
            // Use market.marketType which is correctly passed in
            if (market.marketType === 'SELECTED_ROUND_SCORES_IMG' ||
                market.marketType === 'HOLE_SCORE_IMG' ||
                market.marketType === 'NUMBER_OF_BIRDIES_(OR_BETTER)_IN_THE_ROUND_IMG') {
                league = 'Golf';
            } else if (league === 'NCAA Football Games') league = 'CFB';
            else if (league === 'NCAA Basketball Mens Games') league = 'NCAAB';
            else if (league === 'USA - WNBA') league = 'WNBA';
            else if (league === 'Mens Olympics 2024') league = 'OBBALL';
            else if (league === 'MLB Games' || league === 'MLB') league = 'MLB'; // Standardize MLB
            // --- End Correction ---


            const cleanPlayerName = playerName.includes('(')
                ? playerName.substring(0, playerName.indexOf('(')).trim()
                : playerName;

            const isAltMarket =
                market.marketName.toLowerCase().includes('alt') ||
                (market.marketType && market.marketType.toLowerCase().includes('alt'));

            let startTime = null;
             try {
                 if (market.marketTime) {
                    startTime = new Date(market.marketTime);
                    if (isNaN(startTime.getTime())) {
                        startTime = null;
                        // console.warn(`[FD DEBUG] Invalid date found for market ${market.marketId}: ${market.marketTime}`);
                    }
                 } else {
                     // console.warn(`[FD DEBUG] Missing marketTime for market ${market.marketId}`);
                 }
             } catch (dateError) {
                 // console.error(`[FD DEBUG] Error parsing date for market ${market.marketId}: ${market.marketTime}`, dateError);
                 startTime = null;
             }
             if (!startTime && event && event.openDate) {
                 try {
                     startTime = new Date(event.openDate);
                     if (isNaN(startTime.getTime())) startTime = null;
                 } catch(e){ startTime = null; }
             }
             if (!startTime) {
                  // console.error(`[FD DEBUG SKIP] Cannot determine valid start time for market ${market.marketId}. Skipping.`);
                  return null;
             }


            const overRunner = market.runners.find((r) => r.runnerName.toLowerCase().includes('over'));
            const underRunner = market.runners.find((r) => r.runnerName.toLowerCase().includes('under'));

            if (!overRunner || !underRunner) {
                return null;
            }

            // ---- START DETAILED LOGGING FOR LINE EXTRACTION ----
            let line = null;
            const isPitcherOutsMarket = market.marketType === 'PITCHER_B_OUTS_RECORDED_SB';

            if (isPitcherOutsMarket) {
                 // console.log(`[FD DEBUG PITCHER OUTS] Processing Market ID: ${market.marketId}, Name: ${market.marketName}`);
                 // console.log(`[FD DEBUG PITCHER OUTS] Over Runner Name: ${overRunner?.runnerName}`);
            }

            // Determine where to get the line value based on market type
            if (market.marketType === 'SELECTED_ROUND_SCORES_IMG' ||
                market.marketType === 'NUMBER_OF_BIRDIES_(OR_BETTER)_IN_THE_ROUND_IMG' ||
                isPitcherOutsMarket) {

                const overRunnerName = overRunner.runnerName.toLowerCase();
                const match = overRunnerName.match(/over\s+(\d+\.?\d*)/i);

                if (isPitcherOutsMarket) {
                    // console.log(`[FD DEBUG PITCHER OUTS] Regex Match Result:`, match);
                }

                if (match && match[1]) {
                    line = parseFloat(match[1]);
                     if (isPitcherOutsMarket) {
                        // console.log(`[FD DEBUG PITCHER OUTS] Extracted Line Value: ${line}`);
                     }
                } else {
                     if (isPitcherOutsMarket) {
                        // console.warn(`[FD DEBUG PITCHER OUTS] Failed to extract line from runnerName: ${overRunner.runnerName}`);
                     }
                     line = null;
                }
            } else {
                // For most other types, use handicap if it's non-zero and valid
                 if (typeof overRunner.handicap === 'number' && overRunner.handicap !== 0) {
                     line = overRunner.handicap;
                 } else if (typeof underRunner.handicap === 'number' && underRunner.handicap !== 0) {
                    line = underRunner.handicap; // Should be the same as over
                 }
                 // Fallback to marketName only if handicap is missing/zero (less common/reliable for O/U)
                 if (line === null) {
                    const lineMatch = market.marketName.match(/(\d+\.?\d*)$/); // Try matching number at the end
                     if (lineMatch && lineMatch[1]) {
                         // console.log(`[FD DEBUG] Using fallback line extraction from marketName for ${market.marketId}`);
                         line = parseFloat(lineMatch[1]);
                     }
                 }
            }

            if (isPitcherOutsMarket) {
                // console.log(`[FD DEBUG PITCHER OUTS] Final Line Value for ${market.marketId}: ${line}`);
            }
            // ---- END DETAILED LOGGING ----

            // Validate the extracted line
            if (line === null || isNaN(line) || !isFinite(line)) {
                 if (isPitcherOutsMarket || statType === 'Outs Recorded') { // Log specifically if it's an Outs market failing
                    // console.error(`[FD DEBUG SKIP] Invalid or missing line (value: ${line}) for Market ID: ${market.marketId}, Player: ${playerName}, Stat: ${statType}. Skipping projection.`);
                 }
                 return null; // Skip if line is bad
            }

            // Validate Odds
            const overWinOdds = overRunner.winRunnerOdds;
            const underWinOdds = underRunner.winRunnerOdds;
            if (!overWinOdds || !underWinOdds) {
                 if (isPitcherOutsMarket) {
                    // console.warn(`[FD DEBUG SKIP] Skipping ${market.marketId} due to missing odds objects.`);
                 }
                return null;
            }

            const overOdds = overWinOdds.americanDisplayOdds?.americanOdds ?? overWinOdds.americanDisplayOdds?.americanOddsInt; // More robust check
            const underOdds = underWinOdds.americanDisplayOdds?.americanOdds ?? underWinOdds.americanDisplayOdds?.americanOddsInt;
            const overDecimalOdds = overWinOdds.trueOdds?.decimalOdds?.decimalOdds;
            const underDecimalOdds = underWinOdds.trueOdds?.decimalOdds?.decimalOdds;

            // Check if any odds value is missing or invalid
            if (overOdds == null || underOdds == null || overDecimalOdds == null || underDecimalOdds == null) {
                if (isPitcherOutsMarket) {
                     // console.warn(`[FD DEBUG SKIP] Skipping ${market.marketId} due to missing odds values.`);
                }
                return null;
            }

            const formattedOverOdds = formatOdds(overOdds);
            const formattedUnderOdds = formatOdds(underOdds);
            if (formattedOverOdds === 'N/A' || formattedUnderOdds === 'N/A') {
                 if (isPitcherOutsMarket) {
                    // console.warn(`[FD DEBUG SKIP] Skipping ${market.marketId} due to N/A formatted odds.`);
                 }
                return null;
            }

            // Construct the final projection object
            const projectionData = {
                proj_id: `${market.marketId}_${line}`,
                player_name: cleanPlayerName,
                stat_type: statType.trim(), // Use the statType passed into the function
                line: line,
                over_odds_american: formattedOverOdds,
                under_odds_american: formattedUnderOdds,
                over_odds_decimal: overDecimalOdds,
                under_odds_decimal: underDecimalOdds,
                league: league,
                matchup: event?.name || 'Unknown Game',
                start_time: startTime, // Keep as Date object
                is_alt: isAltMarket,
                source: 'Fanduel'
            };

            // --- Log the final object before returning ---
            if (isPitcherOutsMarket) {
                 // console.log(`[FD DEBUG FINAL OBJECT] FINAL PROJECTION OBJECT for ${market.marketId}:`, JSON.stringify(projectionData, null, 2));
            }
            // --- End Log ---

            return projectionData;

        } catch (error) {
            // console.error(`[FD DEBUG ERROR] Error in extractOverUnderProjection for market ${market?.marketId}:`, error);
            return null;
        }
    }


    /**
     * Extract hole-level bets from a "HOLE_SCORE_IMG" type market.
     */
    extractHoleScoreProjection(market, playerName, statType, events, competitions) {
        const results = [];
        try {
            if (!market.runners || market.runners.length < 1) {
                return results;
            }

            const event = events[market.eventId];
            let league = 'Golf';
            const cleanPlayerName = playerName.includes('(')
                ? playerName.substring(0, playerName.indexOf('(')).trim()
                : playerName;

             let startTime = null; // Handle date safely
             try {
                 if (market.marketTime) {
                    startTime = new Date(market.marketTime);
                    if (isNaN(startTime.getTime())) startTime = null;
                 } else if (event && event.openDate) {
                    startTime = new Date(event.openDate);
                    if (isNaN(startTime.getTime())) startTime = null;
                 }
             } catch(e){ startTime = null; }

             if (!startTime) {
                 // console.error(`[FD DEBUG SKIP] Cannot determine valid start time for hole score market ${market.marketId}. Skipping.`);
                 return results; // Cannot proceed without time
             }

            for (const runner of market.runners) {
                const runnerName = runner.runnerName;
                const runnerWinOdds = runner.winRunnerOdds;
                if (!runnerWinOdds) continue;

                const americanOdds = runnerWinOdds.americanDisplayOdds?.americanOdds ?? runnerWinOdds.americanDisplayOdds?.americanOddsInt;
                const decimalOdds = runnerWinOdds.trueOdds?.decimalOdds?.decimalOdds;
                const formattedOdds = formatOdds(americanOdds);

                if (americanOdds == null || decimalOdds == null || formattedOdds === 'N/A') {
                    continue;
                }

                const specificStatType = `${statType} - ${runnerName}`;

                results.push({
                    proj_id: `${market.marketId}_${runner.selectionId}`,
                    player_name: cleanPlayerName,
                    stat_type: specificStatType.trim(),
                    line: null,
                    over_odds_american: null,
                    under_odds_american: null,
                    single_outcome_odds_american: formattedOdds,
                    single_outcome_odds_decimal: decimalOdds,
                    league: league,
                    matchup: event?.name || 'Unknown Game',
                    start_time: startTime, // Store as Date object
                    is_alt: false,
                    source: 'Fanduel'
                });
            }
            return results;
        } catch (error) {
            // console.error(`[FD DEBUG ERROR] Error extracting hole score projection for market ${market?.marketId}:`, error);
            return results;
        }
    }
}

module.exports = { FanduelSharpAPI };