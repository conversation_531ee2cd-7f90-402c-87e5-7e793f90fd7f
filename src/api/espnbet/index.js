const { RequestManager } = require('../../services/RequestManager');
const { join } = require('path');

// ESPNBetAPI class
class ESPNBetAPI {
    constructor() {
        this.leagueConfig = new Map([
            ['MLB', {
                listUrl: "https://sportsbook-espnbet.us-default.thescore.bet/graphql/persisted_queries/3dbeedc693fb01b8f5753e29a969bf9567cca71357a45b07612d0acb4ce97174",
                listParams: {
                    operationName: "Marketplace",
                    variables: JSON.stringify({
                        includeSectionDefaultField: true,
                        includeDefaultChild: true,
                        canonicalUrl: "/sport/baseball/organization/united-states/competition/mlb",
                        oddsFormat: "AMERICAN",
                        pageType: "PAGE",
                        includeRichEvent: true,
                        includeMediaUrl: false,
                        selectedFilterId: ""
                    }),
                    extensions: JSON.stringify({
                        persistedQuery: {
                            version: 1,
                            sha256Hash: "3dbeedc693fb01b8f5753e29a969bf9567cca71357a45b07612d0acb4ce97174"
                        }
                    })
                },
                eventUrlPath: "/sport/baseball/organization/united-states/competition/mlb/event/{eventId}/section/player_props"
            }],
            ['WNBA', {
                listUrl: "https://sportsbook-espnbet.us-default.thescore.bet/graphql/persisted_queries/3dbeedc693fb01b8f5753e29a969bf9567cca71357a45b07612d0acb4ce97174",
                listParams: {
                    operationName: "Marketplace",
                    variables: JSON.stringify({
                        includeSectionDefaultField: true,
                        includeDefaultChild: true,
                        canonicalUrl: "/sport/basketball/organization/united-states/competition/wnba",
                        oddsFormat: "AMERICAN",
                        pageType: "PAGE",
                        includeRichEvent: true,
                        includeMediaUrl: false,
                        selectedFilterId: ""
                    }),
                    extensions: JSON.stringify({
                        persistedQuery: {
                            version: 1,
                            sha256Hash: "3dbeedc693fb01b8f5753e29a969bf9567cca71357a45b07612d0acb4ce97174"
                        }
                    })
                },
                eventUrlPath: "/sport/basketball/organization/united-states/competition/wnba/event/{eventId}/section/player_props"
            }],
            /*
            ['SOCCER', {
                listUrl: "https://sportsbook-espnbet.us-default.thescore.bet/graphql/persisted_queries/3dbeedc693fb01b8f5753e29a969bf9567cca71357a45b07612d0acb4ce97174",
                listParams: {
                    operationName: "Marketplace",
                    variables: JSON.stringify({
                        includeSectionDefaultField: true,
                        includeDefaultChild: true,
                        canonicalUrl: "/sport/soccer/organization/england/competition/premier-league",
                        oddsFormat: "AMERICAN",
                        pageType: "PAGE",
                        includeRichEvent: true,
                        includeMediaUrl: false,
                        selectedFilterId: ""
                    }),
                    extensions: JSON.stringify({
                        persistedQuery: {
                            version: 1,
                            sha256Hash: "3dbeedc693fb01b8f5753e29a969bf9567cca71357a45b07612d0acb4ce97174"
                        }
                    })
                },
                eventUrlPath: "/sport/soccer/organization/england/competition/premier-league/event/{eventId}/section/player_props"
            }],
            */
            ['CFB', {
                listUrl: "https://sportsbook-espnbet.us-default.thescore.bet/graphql/persisted_queries/2c5f0a65fbc6ae699c8551a106adfb2e3930bbf023d5004bba8947f185831a8d",
                listParams: {
                    operationName: "Marketplace",
                    variables: JSON.stringify({
                        includeSectionDefaultField: true,
                        includeDefaultChild: true,
                        canonicalUrl: "/sport/football/organization/united-states/competition/ncaaf",
                        oddsFormat: "AMERICAN",
                        pageType: "PAGE",
                        includeRichEvent: true,
                        includeMediaUrl: false,
                        selectedFilterId: ""
                    }),
                    extensions: JSON.stringify({
                        persistedQuery: {
                            version: 1,
                            sha256Hash: "2c5f0a65fbc6ae699c8551a106adfb2e3930bbf023d5004bba8947f185831a8d"
                        }
                    })
                },
                eventUrlPath: "/sport/football/organization/united-states/competition/ncaaf/event/{eventId}/section/player_props"
            }],
            ['NFL', {
                listUrl: "https://sportsbook-espnbet.us-default.thescore.bet/graphql/persisted_queries/2c5f0a65fbc6ae699c8551a106adfb2e3930bbf023d5004bba8947f185831a8d",
                listParams: {
                    operationName: "Marketplace",
                    variables: JSON.stringify({
                        includeSectionDefaultField: true,
                        includeDefaultChild: true,
                        canonicalUrl: "/sport/football/organization/united-states/competition/nfl",
                        oddsFormat: "AMERICAN",
                        pageType: "PAGE",
                        includeRichEvent: true,
                        includeMediaUrl: false,
                        selectedFilterId: ""
                    }),
                    extensions: JSON.stringify({
                        persistedQuery: {
                            version: 1,
                            sha256Hash: "2c5f0a65fbc6ae699c8551a106adfb2e3930bbf023d5004bba8947f185831a8d"
                        }
                    })
                },
                eventUrlPath: "/sport/football/organization/united-states/competition/nfl/event/{eventId}/section/player_props"
            }]
            // ... [Add other leagues as needed]
        ]);
    }


    // Method to fetch details using RequestManager
    async fetchDetailsWithProxy(url) {
        try {
            const requestManager = RequestManager.getInstance();
            const response = await requestManager.fetch('espnbet', url, {
                method: 'GET',
                headers: {
                    "accept": "application/json",
                    "accept-language": "en-US,en;q=0.9,la;q=0.8,en-GB;q=0.7",
                    "cache-control": "no-cache",
                    "content-type": "application/json",
                    "origin": "https://espnbet.com",
                    "pragma": "no-cache",
                    "priority": "u=1, i",
                    "referer": "https://espnbet.com/",
                    "sec-ch-ua": "\"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\"",
                    "sec-ch-ua-mobile": "?0",
                    "sec-ch-ua-platform": "\"Windows\"",
                    "sec-fetch-dest": "empty",
                    "sec-fetch-mode": "cors",
                    "sec-fetch-site": "cross-site",
                    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36",
                    "x-anonymous-authorization": "Bearer eyJhbGciOiJSU0EtT0FFUCIsImVuYyI6IkExMjhDQkMtSFMyNTYifQ.OL20gPvb3PtcBikdkAdklrcH8J64XJK5Xyo_VSzqmWhaQcPLxv4p6RJLrbLBBUY8XHDBdCYE5QBC5v6Do181JYwKrlmAHCaQYD-OphJGU4OhcS0NrmviEsTnkI3YS5f2M6ynnpzxd6ICQwYT-SwigWbUiDSQ9F3oU0KXve_3LKq0PRNI08_DI_nZJ7W_7dtvVWbkv-lz0PDAU_uOjmzKOxIIbN_OOkLnmFLdkxFLcMT55ndvmvD-AjM7sqYrAKtH4k0ZyZs4m6i-nldw_Bf0F8lrNuvIh2kGFMRZBgkteWJ4tNLCscnUwnADvWydFgC3fwuzD2KorMTI2ThwJ2jY_ptbuRLmw9DfHkckBrfIxUmRG1kffMp8WNxkcMsMPhU0-Ln9_pSyhbDfg8tVf5GQpwSiaJnBXYKncZX_m1c4JSqQTTDzusvnssdQSp76s0I2AmCxv8cXehDUKO1YEuJEZVx3jp1wtGEsmBWGnb-735Y8Z7MNYXUwQWBARJKDm0sobE9ZTu2um_2DqDFXScd7kTimK03LqO-ntvdzKWCB-_NHsZXMiAWZIGTaO-MHdrEe_13tVj5Jw51vbwSJJvrsyrxEz4QSne0jTn7SdC50zz9_TyvZIw7tlbt56JpGYB8VlWj4djBvIFfQQ1iofv98zQ8Cw1NUIiYJFGn5YzOkplA.OOhUjpbBB_uwRMqMXP6T4Q.IvRmwrPtC_ZycuIHBTgRQujAX4X_kboGvBZmbSVC8Fr3HYNFP83z8FgYYST6Pzwwf7CJOcVHSrMCTYK2WWSFtS6IFrgTzHwXYFqET6i7BteLagQXjGX5VPbcrqkd28CJ0qEaV7f4oR5fFtG27fJ_t09lm42qLoKVmT6IegLz6eHmzgGSUTvOL7R5_V_72uW2eoZdQTQGyfGOtTUjTJhZAx5ODHwCV6x4CBlvlpG0BsN9RgODNMW4U2iVAiJ6hyUc.9stVkBecClUCAgIYcjZvRw",
                    "x-app": "espnbet",
                    "x-app-version": "24.16.0",
                    "x-platform": "web"
                },
                timeout: 15000,
                retries: 2
            });

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('Error fetching details with RequestManager:', error);
            return null;
        }
    }

    // Method to fetch ESPNBet projections
    async fetchESPNBet() {
        let allProjections = [];

        try {
            // Process leagues in parallel using Promise.all
            const leaguePromises = Array.from(this.leagueConfig.entries()).map(async ([league, config]) => {
                console.log(`[ESPNBET] Fetching data for ${league}...`);
                const listUrl = new URL(config.listUrl);
                listUrl.search = new URLSearchParams(config.listParams).toString();

                const responseData = await this.fetchDetailsWithProxy(listUrl.toString());

                if (responseData) {
                    const eventIds = this.extractEventIds(responseData, league); // Pass league to extractEventIds

                    // Fetch event projections in parallel
                    const eventProjections = await Promise.all(
                        eventIds.map(eventId => this.fetchEventProjections(league, eventId))
                    );

                    // Clean up each projection before adding to allProjections
                    eventProjections.flat().forEach(projection => {
                        if (projection.stat_type && projection.player_name) {
                            // Create a regex to remove the entire stat_type from the end of the player_name
                            const statTypeRegex = new RegExp(projection.stat_type + '$', 'i');
                            projection.player_name = projection.player_name.replace(statTypeRegex, '').trim();

                            // Remove any lingering extra spaces
                            projection.player_name = projection.player_name.replace(/\s\s+/g, ' ').trim();
                        }
                        allProjections.push(projection);
                    });
                } else {
                    console.log(`[ESPNBET] Failed to get a valid response for ${league}.`);
                }
            });

            await Promise.all(leaguePromises);
        } catch (error) {
            console.error('[ESPNBET] Error fetching ESPNBet projections:', error);
        }

        return allProjections;
    }

    // Method to extract event IDs from response data
    extractEventIds(data, league) {
        try {
            const marketplaceShelf = data.data.page.defaultChild.sectionChildren.find(
                child => child.__typename === "MarketplaceShelf"
            );

            if (!marketplaceShelf) {
                console.log("[ESPNBET] MarketplaceShelf not found in the response");
                return [];
            }

            return marketplaceShelf.marketplaceShelfChildren
                .filter(child => {
                    // Check if the league is CFB (NCAAF) and filter by "PARLAY_PLUS_ELIGIBLE"
                    if (league === 'CFB') {
                        return child.attributes && child.attributes.includes("PARLAY_PLUS_ELIGIBLE");
                    }
                    return true; // Include all events for other leagues
                })
                .map(child => {
                    const webUrl = child.deepLink.webUrl;
                    return webUrl.split('/').pop();
                });
        } catch (error) {
            console.error(`[ESPNBET] Error extracting event IDs: ${error}`);
            return [];
        }
    }


    async fetchEventProjections(league, eventId) {
        const eventUrl = this.generateEventUrl(league, eventId);
        const responseData = await this.fetchDetailsWithProxy(eventUrl);

        if (responseData) {
            return this.extractProjections(responseData, league);
        } else {
            console.log(`[ESPNBET] Failed to get data for ${league} event ${eventId}`);
            return [];
        }
    }

    // Method to generate event URL
    generateEventUrl(league, eventId) {
        const config = this.leagueConfig.get(league);
        if (!config) {
            throw new Error(`[ESPNBET] No configuration found for league: ${league}`);
        }

        const url = new URL(config.listUrl);
        const params = new URLSearchParams(config.listParams);

        const variables = JSON.parse(params.get('variables'));
        variables.canonicalUrl = config.eventUrlPath.replace('{eventId}', eventId);
        params.set('variables', JSON.stringify(variables));

        url.search = params.toString();
        return url.toString();
    }

    extractProjections(data, league) {
        const projections = [];
        const sections = data?.data?.page?.defaultChild?.sectionChildren || [];

        for (const section of sections) {
            const statType = section?.labelText;

            const drawers = section?.drawerChildren || [];
            for (const drawer of drawers) {
                const marketplaceShelves = drawer?.marketplaceShelfChildren || [];
                for (const shelf of marketplaceShelves) {
                    const matchup = shelf?.fallbackEvent?.name;
                    const startTime = shelf?.fallbackEvent?.startTime;
                    const eventStartTime = new Date(startTime);
                    const markets = shelf?.markets || [];
                    let playerName = '';

                    for (const market of markets) {
                        const selections = market?.selections || [];
                        const overSelection = selections.find(selection => selection.name.cleanName === 'Over');
                        const underSelection = selections.find(selection => selection.name.cleanName === 'Under');

                        if (league === 'NFL' && shelf?.participant?.fullName) {
                            // Use participant.fullName for NFL
                            playerName = shelf.participant.fullName;
                        } else {
                            // Fallback to extracting from market name for other leagues
                            playerName = market?.name?.split(' Total')[0]?.trim();

                            if (statType && playerName) {
                                const lowerStatType = statType.toLowerCase();
                                const lowerPlayerName = playerName.toLowerCase();

                                const statTypeWords = lowerStatType.split(/\s+/);
                                const playerNameWords = lowerPlayerName.split(/\s+/);

                                const cleanedPlayerNameWords = playerNameWords.filter(word => !statTypeWords.includes(word));

                                playerName = cleanedPlayerNameWords.join(' ');

                                // Capitalize the first letter of each word
                                playerName = playerName.replace(/\b\w/g, c => c.toUpperCase());
                            }
                        }


                        const overOddsAmerican = overSelection?.odds?.formattedOdds === 'Even' ? '+100' : overSelection?.odds?.formattedOdds;
                        const underOddsAmerican = underSelection?.odds?.formattedOdds === 'Even' ? '+100' : underSelection?.odds?.formattedOdds;

                        if (overSelection && underSelection && playerName) {
                            projections.push({
                                proj_id: market?.id,
                                league: league,
                                player_name: playerName,
                                stat_type: statType,
                                line: overSelection?.points?.decimalPoints,
                                over_odds_american: overOddsAmerican,
                                under_odds_american: underOddsAmerican,
                                over_odds_decimal: this.americanToDecimal(overOddsAmerican),
                                under_odds_decimal: this.americanToDecimal(underOddsAmerican),
                                start_time: eventStartTime,
                                matchup: matchup,
                                source: 'ESPNBet'
                            });
                        }
                    }
                }
            }
        }

        return projections;
    }

    // Helper method to convert American odds to decimal
    americanToDecimal(americanOdds) {
        if (americanOdds === '+100' || americanOdds === 100) {
            return 2;
        } else if (americanOdds > 100 || americanOdds < -100) {
            return americanOdds > 0 ? (americanOdds / 100) + 1 : (100 / -americanOdds) + 1;
        } else {
            return americanOdds;
        }
    }
}

module.exports = { ESPNBetAPI };
