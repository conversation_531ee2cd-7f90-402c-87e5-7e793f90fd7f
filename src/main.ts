// Set environment optimizations  
process.env.UV_THREADPOOL_SIZE = '8'; // Reduced from 64 - network I/O is non-blocking
process.env.NODE_OPTIONS = '--max-old-space-size=2048'; // 2GB heap size

const pLimit = require('p-limit');

import {
  fetchSharpProjections,
  processProjections,
} from "./utils/ProjectionUtility";
import { logInfo, logError, logWarn, logPerformance } from "./utils/logger";
import { Provider, providers } from "./providers/providers";
import { SimpleIntervalJob, AsyncTask, ToadScheduler } from "toad-scheduler";
import { Projection } from "./utils/types";
import { ProviderSessionError } from "./utils/errors";
import { RequestManager } from "./services/RequestManager";
import { ProxyManager } from "./services/ProxyManager";
const providerDBClient = require("./database/client");

// Add a cycle counter to track even/odd cycles
let cycleCounter = 0;

// Initialize the request manager and proxy manager
const requestManager = RequestManager.getInstance({
  globalConcurrencyLimit: 50,
  perHostConcurrencyLimit: 5,
  defaultTimeout: 15000,
  keepAliveTimeout: 5000,
  maxSockets: 10,
  proxyRotationThreshold: 10,
});

const proxyManager = ProxyManager.getInstance({
  cooldownPeriod: 5 * 60 * 1000,
  maxConsecutiveFailures: 3,
  successRateThreshold: 0.5,
  latencyThreshold: 10000,
  proxyStickiness: 10,
});

// Periodic cleanup tasks
setInterval(async () => {
  await proxyManager.cleanup();
  const stats = proxyManager.getStats();
  logInfo('Proxy stats', stats);
}, 60 * 60 * 1000); // Every hour

const findPlays = async (provider: Provider, sharpProjections: Projection[]) => {
  const startTime = Date.now();
  let providerProjections: Projection[] = [];
  let fetchDuration = 0;
  let processDuration = 0;
  let storeDuration = 0;

  try {
    // Fetching provider projections
    const fetchStartTime = Date.now();
    providerProjections = await Promise.race([
      provider.fetchFunction(sharpProjections),
      new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error('Fetch timeout')), 75000)
      )
    ]);
    fetchDuration = Date.now() - fetchStartTime;

    // Log total projections once after fetching
    logInfo(`${provider.name}: Total Projections - ${providerProjections.length}`);

    // Processing all projections at once
    const processStartTime = Date.now();
    const plays = processProjections(sharpProjections, providerProjections, provider.name);
    
    processDuration = Date.now() - processStartTime;

    // Log total EV bets found once after processing
    logInfo(`${provider.name}: Total EV bets found - ${plays.length}`);

    // Storing projections (only if plays exist)
    if (plays.length > 0) {
      const storeStartTime = Date.now();
      await providerDBClient.storeProjections(
        provider.name,
        plays,
        provider.collectionName
      );
      storeDuration = Date.now() - storeStartTime;
    }

  } catch (error: unknown) {
    if (error instanceof Error) {
      if (error.message === 'Fetch timeout') {
        logWarn(`${provider.name}: Fetch timed out after 75 seconds`);
      } else {
        logError(`${provider.name}: Error during execution`, error);
      }
    } else {
      logError(`${provider.name}: An unknown error occurred`);
    }
  } finally {
    // Log the breakdown regardless of success or failure
    const totalDuration = Date.now() - startTime;
    logPerformance(`${provider.name}_cycle`, totalDuration, {
      fetch: fetchDuration,
      process: processDuration,
      store: storeDuration,
      projections: providerProjections.length,
    });
    
    if (providerProjections.length === 0) {
      logWarn(`${provider.name}: Warning - No projections fetched`);
    }
  }
};

const scheduler = new ToadScheduler();

const task = new AsyncTask(
  "scrapers",
  async () => {
    // Increment cycle counter
    cycleCounter++;
    logInfo(`Scraping Projections - Cycle #${cycleCounter}`);

    const sharpProjections = await fetchSharpProjections();

    // Create a concurrency pool with parallel executions
    const limit = pLimit(15);
    
    // Run all providers every cycle
    const providersToRun = providers;
    
    await Promise.all(providersToRun.map(provider => 
      limit(async () => {
        try {
          await findPlays(provider, sharpProjections);
        } catch (error) {
          throw new ProviderSessionError(provider.name, JSON.stringify(error));
        }
      })
    ));
  },
  (err: Error) => {
    console.error({ err });
  }
);

// Increase interval to 60 seconds to prevent overruns
const job = new SimpleIntervalJob({ seconds: 60, runImmediately: true }, task, {
  id: "scrapers",
  preventOverrun: true,
});

// Create and start job
scheduler.addSimpleIntervalJob(job);

// Graceful shutdown logic
process.on("SIGINT", async () => {
  logInfo("Shutting down gracefully...");
  scheduler.stop();
  try {
    await requestManager.destroy();
    await providerDBClient.closeAllConnections(); // Close all database connections
    logInfo("Successfully closed all connections.");
    process.exit(0); // Exit cleanly
  } catch (error) {
    console.error(`Error while closing database connections: ${error}`);
    process.exit(1); // Exit with error
  }
});
