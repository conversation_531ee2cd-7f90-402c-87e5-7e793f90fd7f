import { Worker } from 'worker_threads';
import { cpus } from 'os';
import path from 'path';
import { logInfo, logError } from '../utils/logger';

interface WorkerTask {
  resolve: (value: any) => void;
  reject: (reason: any) => void;
  data: any;
}

export class WorkerPool {
  private workers: Worker[] = [];
  private freeWorkers: Worker[] = [];
  private tasks: WorkerTask[] = [];
  private workerPath: string;
  private maxWorkers: number;

  constructor(workerPath: string, maxWorkers?: number) {
    this.workerPath = workerPath;
    this.maxWorkers = maxWorkers || Math.min(4, cpus().length);
    this.init();
  }

  private init() {
    for (let i = 0; i < this.maxWorkers; i++) {
      this.addNewWorker();
    }
    logInfo(`WorkerPool initialized with ${this.maxWorkers} workers`);
  }

  private addNewWorker() {
    const worker = new Worker(this.workerPath);
    
    worker.on('message', (result) => {
      // Worker is done, make it available again
      this.freeWorkers.push(worker);
      this.runNext();
    });

    worker.on('error', (error) => {
      logError('Worker error', error);
      // Remove the errored worker and create a new one
      const index = this.workers.indexOf(worker);
      if (index !== -1) {
        this.workers.splice(index, 1);
      }
      this.addNewWorker();
    });

    this.workers.push(worker);
    this.freeWorkers.push(worker);
  }

  async exec(data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.tasks.push({ resolve, reject, data });
      this.runNext();
    });
  }

  private runNext() {
    if (this.tasks.length === 0) {
      return;
    }

    if (this.freeWorkers.length === 0) {
      return;
    }

    const worker = this.freeWorkers.shift()!;
    const task = this.tasks.shift()!;

    const messageHandler = (result: any) => {
      worker.off('message', messageHandler);
      worker.off('error', errorHandler);
      
      if (result.success) {
        task.resolve(result.results);
      } else {
        task.reject(new Error(result.error));
      }
    };

    const errorHandler = (error: Error) => {
      worker.off('message', messageHandler);
      worker.off('error', errorHandler);
      task.reject(error);
    };

    worker.once('message', messageHandler);
    worker.once('error', errorHandler);
    worker.postMessage(task.data);
  }

  async terminate() {
    await Promise.all(this.workers.map(worker => worker.terminate()));
    this.workers = [];
    this.freeWorkers = [];
    this.tasks = [];
    logInfo('WorkerPool terminated');
  }
}