const { parentPort } = require('worker_threads');
const { MetricUtility } = require('../utils/MetricUtility');
const { calculateSharpEVs } = require('../sharps/helper');

// Listen for messages from the main thread
parentPort.on('message', ({ projections, modelInfo, sharps }) => {
  try {
    const results = [];
    
    for (const projection of projections) {
      let probabilityFunction;
      
      if (modelInfo.model === 'negbin') {
        probabilityFunction = (k, r) => MetricUtility.negativeBinomialPmf(k, r, modelInfo.p);
      } else if (modelInfo.model === 'poisson') {
        probabilityFunction = MetricUtility.poissonPmf;
      } else {
        continue;
      }

      // Calculate EVs using the sharp projections
      const sharpEVs = calculateSharpEVs(
        projection,
        sharps,
        modelInfo,
        probabilityFunction
      );

      if (sharpEVs && sharpEVs.length > 0) {
        results.push({
          projection,
          sharpEVs
        });
      }
    }

    // Send results back to main thread
    parentPort.postMessage({ success: true, results });
  } catch (error) {
    parentPort.postMessage({ 
      success: false, 
      error: error.message 
    });
  }
});