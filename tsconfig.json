{"compilerOptions": {"target": "es2016", "module": "commonjs", "allowJs": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "noImplicitAny": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node"}, "include": ["src/**/*"], "exclude": ["node_modules", "**/node_modules/**", "src/**/node_modules/**", "**/*.spec.ts", "dist"]}